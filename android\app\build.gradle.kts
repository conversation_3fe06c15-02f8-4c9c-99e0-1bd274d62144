plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.cyperdeewan.deewan"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        // isCoreLibraryDesugaringEnabled = true  // for old packages like notification
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_21.toString()
    }

  sourceSets {
        getByName("main").java.srcDirs("src/main/kotlin")
    } 

    defaultConfig {
        // Specify your own unique Application ID.
        applicationId = "com.cyperdeewan.deewan"
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }


    buildTypes {
        release {
            // This ensures the release build is signed with your release key.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}
// dependencies {
//    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.4")
// }
flutter {
    source = "../.."
}
