 import 'package:deewan/app/services/services_impl/app_settings_service.dart';
import 'package:deewan/app/services/services_impl/localization_service.dart';
import 'package:deewan/core/network/api_client.dart';
import 'package:deewan/core/network/network_info.dart';
import 'package:deewan/core/storage/secure_storage.dart';
import 'package:refreshed/refreshed.dart';
import 'package:http/http.dart' as http;

/// Main dependency injection bindings for the entire application
/// This is the single source of truth for all app-wide dependencies
class Initialbindings extends Binding {
  @override
  List<Bind> dependencies() => [
    // ========================================
    // CORE INFRASTRUCTURE DEPENDENCIES
    // ========================================

    // HTTP Client - Foundation for all network operations
    Bind.put(() => http.Client(), permanent: true),

    // Network connectivity monitoring
    Bind.put(() => NetworkInfoImpl(), permanent: true),

    // ========================================
    // STORAGE LAYER DEPENDENCIES
    // ========================================

    // Secure storage for sensitive data (tokens, credentials)
    Bind.put(() => SecureStorageImpl(), permanent: true),

    // ObjectBox store for local database operations
    Bind.put(() => ObjectBoxStore.instance, permanent: true),

    // ========================================
    // NETWORK LAYER DEPENDENCIES
    // ========================================

    // API client for REST operations
    Bind.put(() => ApiClient(client: Get.find<http.Client>()), permanent: true),

    // ========================================
    // APPLICATION SERVICES
    // ========================================

    // ObjectBox service - Database operations (initialized lazily)
    Bind.lazyPut<ObjectboxService>(() {
      final service = ObjectboxService();
      // Service will call ensureInitialized() when first accessed
      return service;
    }, fenix: true),

    // Settings service - App configuration management (depends on ObjectBox)
    Bind.lazyPut<AppSettingsService>(() {

      final service = AppSettingsService();
      // Service will call ensureInitialized() when first accessed
      return service;
    }, fenix: true),

    // Localization service - Multi-language support
    Bind.lazyPut<LocalizationService>(() => LocalizationService(), fenix: true),

    // ========================================
    // CONTROLLERS - App-wide state management
    // ========================================

    // Settings controller - Global app settings state
    Bind.put(() => SettingsController(), permanent: true),


    // ========================================
    // FUTURE DEPENDENCIES (Placeholders)
    // ========================================

    // TODO: Authentication & Security
    // Bind.lazyPut<AuthRepository>(() => AuthRepositoryImpl(
    //   apiClient: Get.find<ApiClient>(),
    //   secureStorage: Get.find<SecureStorage>(),
    // ), fenix: true),
    // Bind.put(() => AuthController(), permanent: true),

    // TODO: Real-time Communication
    // Bind.put(() => WebSocketController(), permanent: true),
    // Bind.put(() => NotificationController(), permanent: true),

    // TODO: App State Management
    // Bind.put(() => NetworkController(), permanent: true),
    // Bind.put(() => NavigationController(), permanent: true),
    // Bind.put(() => AppStateController(), permanent: true),
  ];

  /// Optional: Initialize critical services early if needed
  /// This can be called before the app starts for services that must be ready immediately
  static Future<void> initCriticalServices() async {
    // Force initialization of critical services if needed
    // await Get.find<ObjectboxService>().ensureInitialized();
    // await Get.find<SettingsService>().ensureInitialized();
  }
}
