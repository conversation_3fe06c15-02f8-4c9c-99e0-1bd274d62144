import 'package:deewan/core/assets/imageassets.dart';
import 'package:refreshed/refreshed.dart';
import '../models/boarding_model.dart';

List<OnBoardingModel> onBoardingList = [
  OnBoardingModel(
    title: 'WelcometoDeewan'.tr,
    description:
        'Flutter is an open-source framework for building high-performance, Flutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crossFlutter is an open-source framework for building high-performance, crosscross-platform apps with Material Design. Flutter is used by developers to create high-quality, high-fidelity, apps for iOS, Android, Web, and desktop.Flutter is an open-source framework for building high-performance, cross-platform apps with Material Design. Flutter is used by developers to create high-quality, high-fidelity, apps for iOS, Android, Web, and desktop',
    image: ImageAsset.onboarding,
  ),
];

const List cardelements = [
  [
    "element",
    "chips",
    ["diabetis", "hypertention", "allergy", "CKD", "CAD"]
  ],
  [
    "how many?",
    "number",
    ["a lot", "a few", " about half"]
  ],
  [
    "since when?",
    "time",
    ["today", "yesterday", "lastweek", "lastmonth"]
  ]
];
