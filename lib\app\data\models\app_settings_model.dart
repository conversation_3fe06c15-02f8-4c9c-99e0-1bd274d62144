// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:isar_community/isar.dart';

part 'app_settings_model.g.dart';

//flutter pub run build_runner build

@collection
class AppSettings {
  Id id = Isar.autoIncrement;

  // ========================================
  // CORE APP SETTINGS
  // ========================================

  /// Device identification and basic settings
  String? deviceName;
  String? passCodeLock;
  bool? isLoggedIn;

  // ========================================
  // APPEARANCE & THEME SETTINGS
  // ========================================

  /// Theme preferences
  bool? isDarkMode;
  String? customThemeId;
  String? accentColor; // Hex color code
  double? fontSize;
  bool? useSystemTheme;

  // ========================================
  // LOCALIZATION SETTINGS
  // ========================================

  /// Language and localization
  String? language; // e.g., 'en_US', 'ar_SA'
  String? localizations;
  bool? useSystemLanguage;

  // ========================================
  // NOTIFICATION SETTINGS
  // ========================================

  /// General notifications
  bool? notificationEnabled;
  bool? soundEnabled;
  bool? vibrationEnabled;
  bool? showNotificationPreview;

  /// Messaging notifications
  bool? messageNotificationsEnabled;
  bool? groupMessageNotificationsEnabled;
  bool? mentionNotificationsEnabled;
  String? messageNotificationSound;

  /// E-commerce notifications
  bool? orderNotificationsEnabled;
  bool? paymentNotificationsEnabled;
  bool? promotionNotificationsEnabled;
  bool? deliveryNotificationsEnabled;

  /// Notification scheduling
  String? quietHoursStart; // HH:mm format
  String? quietHoursEnd;
  bool? weekendQuietMode;

  // ========================================
  // GLOBAL MESSAGING DEFAULTS
  // ========================================

  /// Default messaging behavior (can be overridden per identity)
  bool? defaultSendReadReceipts;
  bool? defaultShowTypingIndicators;
  bool? defaultEnableMessageEncryption;
  bool? defaultAutoDownloadMedia;
  bool? defaultCompressImages;
  String? defaultMediaDownloadPreference; // 'wifi_only', 'always', 'never'

  /// Global chat interface defaults
  bool? defaultEnterToSend;
  bool? defaultShowTimestamps;
  double? defaultChatFontSize;
  bool? defaultShowOnlineStatus;

  /// Global voice & video defaults
  bool? defaultEnableVoiceMessages;
  bool? defaultEnableVideoMessages;
  String? defaultVoiceMessageQuality; // 'low', 'medium', 'high'
  bool? defaultAutoPlayVoiceMessages;

  // ========================================
  // E-COMMERCE SETTINGS
  // ========================================

  /// Payment preferences
  String? defaultPaymentMethod;
  bool? savePaymentMethods;
  bool? requireBiometricForPayments;
  String? preferredCurrency;

  /// Shopping preferences
  String? defaultShippingAddress;
  bool? saveShippingAddresses;
  String? preferredDeliveryTime; // 'morning', 'afternoon', 'evening'
  bool? enableOrderTracking;

  /// Product preferences
  bool? showPriceAlerts;
  bool? enableWishlistSync;
  String?
  productSortPreference; // 'price_low', 'price_high', 'rating', 'newest'
  bool? showOutOfStockItems;

  // ========================================
  // PRIVACY & SECURITY SETTINGS
  // ========================================

  /// Privacy controls
  bool? showLastSeen;
  bool? showProfilePhoto;
  bool? allowContactSync;
  String? whoCanSeeProfile; // 'everyone', 'contacts', 'nobody'
  String? whoCanMessageMe; // 'everyone', 'contacts', 'nobody'

  /// Security features
  bool? enableTwoFactorAuth;
  bool? enableBiometricLogin;
  bool? autoLockEnabled;
  int? autoLockTimeoutMinutes;
  bool? enableScreenshotBlocking;

  /// Data & Analytics
  bool? analyticsEnabled;
  bool? crashReportingEnabled;
  bool? shareUsageData;
  bool? personalizedAds;

  // ========================================
  // SYSTEM & PERFORMANCE SETTINGS
  // ========================================

  /// Background operations
  bool? backgroundWorkEnabled;
  bool? backgroundSyncEnabled;
  String? syncFrequency; // 'realtime', 'hourly', 'daily', 'manual'

  /// Storage & Cache
  bool? autoSaveEnabled;
  int? autoSaveIntervalMinutes;
  int? maxCacheSizeMB;
  bool? autoDeleteOldMessages;
  int? messageRetentionDays;

  /// Network preferences
  String? dataUsagePreference; // 'low', 'medium', 'high'
  bool? wifiOnlySync;
  bool? roamingDataEnabled;

  // ========================================
  // ACCESSIBILITY SETTINGS
  // ========================================

  /// Accessibility features
  bool? highContrastMode;
  bool? largeTextMode;
  bool? screenReaderEnabled;
  bool? reduceMotion;
  bool? hapticFeedbackEnabled;

  // ========================================
  // METADATA
  // ========================================

  /// Settings metadata
  DateTime? lastModified;

  DateTime? createdAt;

  int? settingsVersion; // For migration purposes

  /// Additional custom settings as JSON
  String? customSettings;

  AppSettings({
    this.deviceName,
    this.passCodeLock,
    this.isLoggedIn,

    // Theme defaults
    this.isDarkMode,
    this.customThemeId,
    this.accentColor,
    this.fontSize,
    this.useSystemTheme,

    // Localization defaults
    this.language,
    this.localizations,
    this.useSystemLanguage,

    // Notification defaults
    this.notificationEnabled,
    this.soundEnabled,
    this.vibrationEnabled,
    this.showNotificationPreview,
    this.messageNotificationsEnabled,
    this.groupMessageNotificationsEnabled,
    this.mentionNotificationsEnabled,
    this.messageNotificationSound,
    this.orderNotificationsEnabled,
    this.paymentNotificationsEnabled,
    this.promotionNotificationsEnabled,
    this.deliveryNotificationsEnabled,
    this.quietHoursStart,
    this.quietHoursEnd,
    this.weekendQuietMode,

    // Messaging defaults
    this.defaultSendReadReceipts,
    this.defaultShowTypingIndicators,
    this.defaultEnableMessageEncryption,
    this.defaultAutoDownloadMedia,
    this.defaultCompressImages,
    this.defaultMediaDownloadPreference,
    this.defaultEnterToSend,
    this.defaultShowTimestamps,
    this.defaultChatFontSize,
    this.defaultShowOnlineStatus,
    this.defaultEnableVoiceMessages,
    this.defaultEnableVideoMessages,
    this.defaultVoiceMessageQuality,
    this.defaultAutoPlayVoiceMessages,

    // E-commerce defaults
    this.defaultPaymentMethod,
    this.savePaymentMethods,
    this.requireBiometricForPayments,
    this.preferredCurrency,
    this.defaultShippingAddress,
    this.saveShippingAddresses,
    this.preferredDeliveryTime,
    this.enableOrderTracking,
    this.showPriceAlerts,
    this.enableWishlistSync,
    this.productSortPreference,
    this.showOutOfStockItems,

    // Privacy defaults
    this.showLastSeen,
    this.showProfilePhoto,
    this.allowContactSync,
    this.whoCanSeeProfile,
    this.whoCanMessageMe,
    this.enableTwoFactorAuth,
    this.enableBiometricLogin,
    this.autoLockEnabled,
    this.autoLockTimeoutMinutes,
    this.enableScreenshotBlocking,
    this.analyticsEnabled,
    this.crashReportingEnabled,
    this.shareUsageData,
    this.personalizedAds,

    // System defaults
    this.backgroundWorkEnabled,
    this.backgroundSyncEnabled,
    this.syncFrequency,
    this.autoSaveEnabled,
    this.autoSaveIntervalMinutes,
    this.maxCacheSizeMB,
    this.autoDeleteOldMessages,
    this.messageRetentionDays,
    this.dataUsagePreference,
    this.wifiOnlySync,
    this.roamingDataEnabled,

    // Accessibility defaults
    this.highContrastMode,
    this.largeTextMode,
    this.screenReaderEnabled,
    this.reduceMotion,
    this.hapticFeedbackEnabled,

    // Metadata
    this.lastModified,
    this.createdAt,
    this.settingsVersion,
    this.customSettings,
  });

  /// Create default settings instance
  factory AppSettings.defaults() {
    final now = DateTime.now();
    return AppSettings(
      createdAt: now,
      lastModified: now,
      settingsVersion: 1,

      // Theme defaults
      isDarkMode: false,
      useSystemTheme: true,
      fontSize: 14.0,

      // Notification defaults
      notificationEnabled: true,
      soundEnabled: true,
      vibrationEnabled: true,
      showNotificationPreview: true,
      messageNotificationsEnabled: true,
      groupMessageNotificationsEnabled: true,
      mentionNotificationsEnabled: true,
      orderNotificationsEnabled: true,
      paymentNotificationsEnabled: true,

      // Messaging defaults
      defaultSendReadReceipts: true,
      defaultShowTypingIndicators: true,
      defaultEnableMessageEncryption: true,
      defaultAutoDownloadMedia: true,
      defaultMediaDownloadPreference: 'wifi_only',
      defaultEnterToSend: false,
      defaultShowTimestamps: true,
      defaultChatFontSize: 14.0,
      defaultShowOnlineStatus: true,
      defaultEnableVoiceMessages: true,
      defaultEnableVideoMessages: true,
      defaultVoiceMessageQuality: 'medium',

      // Privacy defaults
      showLastSeen: true,
      showProfilePhoto: true,
      allowContactSync: true,
      whoCanSeeProfile: 'contacts',
      whoCanMessageMe: 'contacts',
      analyticsEnabled: true,
      crashReportingEnabled: true,

      // System defaults
      backgroundWorkEnabled: true,
      backgroundSyncEnabled: true,
      syncFrequency: 'realtime',
      autoSaveEnabled: true,
      autoSaveIntervalMinutes: 5,
      maxCacheSizeMB: 500,
      dataUsagePreference: 'medium',
      wifiOnlySync: false,
    );
  }

  /// Update last modified timestamp
  void touch() {
    lastModified = DateTime.now();
  }
}