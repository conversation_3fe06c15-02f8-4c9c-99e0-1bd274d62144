import 'package:isar_community/isar.dart';

@collection

class OnBoardingModel {
  final String title;
  final String description;
  final String image;

  OnBoardingModel({
    required this.title,
    required this.description,
    required this.image,
  });
}
@Collection()
class RegistrationModel {
  final String? email;
  final String? phone;
  final String password;
  final String userName;
  final String? pinCode;
  final String? verificationCode; // Added for OTP/verification
  final bool isVerified; 
  
  RegistrationModel(this.verificationCode, this.isVerified, {
    required this.email,
    required this.phone,
    required this.password,
    required this.userName,
    this.pinCode,
  });
}