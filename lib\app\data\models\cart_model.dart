//Cart: A dynamic list of products that a customer has selected for purchase. This is a temporary, user-specific collection and is distinct from the store's permanent Collection categories. The cart will also contain a field, fulfillment_employee_id, which is a foreign key to the Identity of the employee responsible for fulfilling the order.
import 'package:deewan/app/data/models/item_models.dart';
import 'package:deewan/app/data/models/identity_model.dart';
import 'package:isar/isar.dart';

part 'cart_model.g.dart';

@collection
class Cart {
  Id id = Isar.autoIncrement;
  
  final String cartId; // Unique identifier

  // Store that this cart belongs to
  final store = IsarLink<Identity>(); // Must be store identity
  
  // Owner of the cart
  final owner = IsarLink<Identity>(); // Can be private or store identity
  
  // Employee responsible for fulfilling (for store-managed carts)
  final fulfillmentEmployee = IsarLink<Identity>(); // Must be private identity
  
  // Cart items
  @Backlink(to: 'cart')
  final items = IsarLinks<CartItem>();
  
  @enumerated
  final CartType cartType; // template, customer, wishlist
  
  @enumerated
  final CartStatus status; // active, abandoned, completed, archived
  
  final String? note;
  final String? customerInstructions;
  
  // Pricing
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  
  final DateTime createdAt;
  
  DateTime updatedAt;
  
  DateTime? completedAt;

  Cart({
    required this.cartId,
    required this.cartType,
    required this.status,
    this.note,
    this.customerInstructions,
    this.subtotal = 0.0,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    this.totalAmount = 0.0,
    required this.createdAt,
    required this.updatedAt,
    this.completedAt,
  });
}

@collection
class CartItem {
  Id id = Isar.autoIncrement;
  final cart = IsarLink<Cart>();
  final itemList = IsarLink<ItemList>(); 
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? customerNote;
  final DateTime addedAt;
  DateTime updatedAt;

  CartItem({
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.customerNote,
    required this.addedAt,
    required this.updatedAt,
  });
}

@enumerated
enum CartType {
  template,    // Store template cart
  wishlist,    // Customer wishlist
  archived,    // Archived carts
  order,       // Completed order cart
  
}

@enumerated
enum CartStatus {
  active,      // Currently being used
  abandoned,   // Left inactive for a period
  completed,   // Order placed/completed
  archived,    // Archived for record keeping
}
// CartModel: Represents a shopping cart in the system, containing items and associated metadata.
// - cartId: Unique identifier for the cart.
// - items: List of items in the cart.
// - fulfillmentEmployee: Employee responsible for fulfilling the order.
// - createdAt: Timestamp when the cart was created.
// - updatedAt: Timestamp when the cart was last updated.
// - isActive: Indicates if the cart is currently active.
// - note: Optional note for the cart.

// CartModel is used to manage the shopping cart functionality, allowing users to add items, view their cart, and proceed to checkout. It is linked to the Identity of the employee who will fulfill the order, ensuring that there is a clear record of who is responsible for processing the cart's contents. The timestamps help track when the cart was created and last modified, which is useful for auditing and user experience purposes.
// The isActive field allows for easy management of the cart's state, enabling features like cartabandonment tracking or reactivation of previously saved carts. The note field provides flexibility for users to add custom information related to their cart, enhancing the overall shopping experience.
// The ToMany<ItemList> allows for a dynamic list of items, which can be modified