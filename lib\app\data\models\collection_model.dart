//Collection: A structured, pre-defined list of products or services within a Store. It is used to categorize and organize products, like "New Arrivals," "Seasonal Sale," or "Electronics." Products are associated with one or more collections.
import 'package:deewan/app/data/models/item_models.dart';
import 'package:deewan/app/data/models/store_page_model.dart';
import 'package:isar/isar.dart';

part 'collection_model.g.dart';

@collection
class CollectionModel {
  Id id = Isar.autoIncrement; // Use Isar's auto-incrementing ID

  @Index(unique: true, caseSensitive: false)
  final String collectionId; // Unique identifier
  final String name;
  final String? description;
  final String? imageUrl;
  final bool isActive;
  final int sortOrder; // For ordering collections in store

  // Belongs to a specific store
  final storePage = IsarLink<StorePage>();

  // Items in this collection
  @Backlink(to: 'collections')
  final items = IsarLinks<Item>();

  final DateTime createdAt;
  
  DateTime updatedAt;

  CollectionModel({
    required this.collectionId,
    required this.name,
    this.description,
    this.imageUrl,
    this.isActive = true,
    this.sortOrder = 0,
    required this.createdAt,
    required this.updatedAt,
  });
}