import 'package:deewan/app/data/models/identity_model.dart';
import 'package:isar_community/isar.dart';

part 'contact_page_model.g.dart';

@collection
class ContactPage {
  Id id = Isar.autoIncrement;
  final String contactPageId; // Unique identifier
  final String? name;
  final String? description;
  final String? bio;
  final String? website;
  final String? location;
  
  // One-to-one relationship with Identity (private identities only)
  final   identity = IsarLink<Identity>();
  
  // Contact permissions - which identities can view this contact page
  @Backlink(to: 'contactPage')
  final viewPermissions = IsarLinks<ContactPermission>();
  
  final DateTime createdAt;
  
  DateTime updatedAt;

  ContactPage({
    required this.contactPageId,
    this.name,
    this.description,
    this.bio,
    this.website,
    this.location,
    required this.createdAt,
    required this.updatedAt,
  });
}

@collection
class ContactPermission {
  Id id = Isar.autoIncrement;
  
  final contactPage = IsarLink<ContactPage>();
  final identity = IsarLink<Identity>(); // Identity that has permission
  final String permissionLevel; // 'view', 'contact', 'blocked'
  
  final DateTime grantedAt;

  ContactPermission({
    required this.permissionLevel,
    required this.grantedAt,
  });
}