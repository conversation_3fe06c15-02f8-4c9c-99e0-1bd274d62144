import 'package:isar/isar.dart';

part 'devices_model.g.dart';

@collection
class Device {
  @Index(unique: true, replace: true)
  final String deviceId;
  
  Id get id => fastHash(deviceId);

  final String name;
  final String type;
  final String status;
  final String location;
  final String lastSeen;
  final String batteryLevel;
  final String? ipAddress;
  final String? macAddress;
  final String os;
  final String? version;
  final String? manufacturer;
  final String? model;  
  final String? serialNumber;
  final String? notes;
  final String createdAt;
  final String updatedAt;

  Device({
    required this.deviceId,
    required this.name,
    required this.type,
    required this.status,
    required this.location,
    required this.lastSeen,
    required this.batteryLevel,
    this.ipAddress,
    this.macAddress,
    required this.os,
    this.version,
    this.manufacturer,
    this.model,
    this.serialNumber,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });
    
}