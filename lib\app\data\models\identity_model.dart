import 'package:deewan/app/data/models/address_model.dart';
import 'package:deewan/app/data/models/room_model.dart';
import 'package:deewan/app/data/models/store_page_model.dart';
import 'package:deewan/app/data/models/user_profile_model.dart';
import 'package:deewan/app/data/models/item_models.dart';
import 'package:deewan/app/data/models/contact_page_model.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:isar_community/isar.dart';

part 'identity_model.g.dart';

@collection
class Identity {
  Id id = Isar.autoIncrement;
  
  @Index() // Index for efficient lookups
  final String identityId; // Unique ID for this specific identity card in the system
  final String name;
  final String phoneNumber; //msg of phone number entity
  final String email; //email msg  entity
  @enumerated
  late IdentityStatus status;
  
  final DateTime lastSeen;
  final String? type; // e.g., 'private' , 'store' 
  final contactPage = IsarLink<ContactPage>();
  final isVerified = false;
  @Backlink(to: 'identity')
  final roomParticipations = IsarLinks<RoomParticipant>();
  final images = IsarLinks<ImageUrl>();
  @Backlink(to: 'identity')
  final storeParticipants = IsarLinks<StoreParticipant>();

  Identity({
    required this.identityId,
    required this.name,
    required this.phoneNumber,
    required this.email,
    required this.status,
    required this.lastSeen,
    this.type,
  });
}

@collection
class MyIdentity {
  Id id = Isar.autoIncrement;
  final String? userName;
  final identityId = IsarLink<Identity>();
  final userProfile = IsarLink<UserPofile>();
  final String? title;
  @Backlink(to: 'identity')
  final rooms = IsarLinks<RoomParticipant>();
  bool isDefault = false; // Whether this is the default identity for new interactions
  final bolckedContacts = IsarLink<ItemList>(); //??
  final contacts = IsarLink<ContactPage>();
  final addresses = IsarLink<Address>();
//  final medicalRecored = IsarLink<ItemList>();
  MyIdentity({this.title, this.isDefault = false, this.userName});
}

@collection
class RoomParticipant {
  Id id = Isar.autoIncrement;
  DateTime joinDate;
  String? role; // e.g., 'member', 'admin'
  int unreadCount; // Per-identity unread count for this room
  DateTime? lastReadMessageTimestamp; // For per-identity read receipts

  // Relationships to IdentityCard and Room (Many-to-One)
  // Indexing these IsarLink relationships' target IDs is crucial for queries.
  final identity = IsarLink<Identity>();
  final room = IsarLink<Room>();

  RoomParticipant({
    required this.joinDate,
    this.role,
    this.unreadCount = 0,
    this.lastReadMessageTimestamp,
  });
}

enum IdentityStatus {
  active,
  inactive,
  blocked,
  incognito,
}