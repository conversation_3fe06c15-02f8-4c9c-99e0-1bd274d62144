// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:isar_community/isar.dart';

part 'identity_settings_model.g.dart';

//flutter pub run build_runner build

@collection
class IdentitySettings {
  Id id = Isar.autoIncrement;

  /// Reference to the MyIdentityCard this settings belongs to
  @Index()
  String myIdentityCardId;

  // ========================================
  // IDENTITY DISPLAY SETTINGS
  // ========================================
  
  /// Display preferences for this identity
  String? displayName;
  String? statusMessage;
  String? avatarUrl;
  String? customThemeForIdentity; // Identity-specific theme override
  
  // ========================================
  // PRIVACY SETTINGS PER IDENTITY
  // ========================================
  
  /// Who can see this identity's information
  String? whoCanSeeProfile; // 'everyone', 'contacts', 'nobody'
  String? whoCanMessageMe; // 'everyone', 'contacts', 'nobody'
  bool? showLastSeen;
  bool? showOnlineStatus;
  bool? showProfilePhoto;
  bool? showStatusMessage;
  
  /// Read receipts and typing indicators for this identity
  bool? sendReadReceipts;
  bool? showTypingIndicators;
  bool? allowForwarding; // Allow messages to be forwarded
  bool? allowScreenshots; // Allow screenshots in chats
  
  // ========================================
  // MESSAGING PREFERENCES PER IDENTITY
  // ========================================
  
  /// Message behavior for this identity
  bool? autoDownloadMedia;
  String? mediaDownloadPreference; // 'wifi_only', 'always', 'never'
  bool? compressImages;
  bool? enableVoiceMessages;
  bool? enableVideoMessages;
  String? voiceMessageQuality; // 'low', 'medium', 'high'
  
  /// Chat interface preferences
  String? chatWallpaper; // Identity-specific wallpaper
  double? chatFontSize; // Identity-specific font size
  bool? showTimestamps;
  bool? enterToSend;
  
  // ========================================
  // NOTIFICATION SETTINGS PER IDENTITY
  // ========================================
  
  /// Notification preferences for this identity
  bool? messageNotificationsEnabled;
  bool? groupMessageNotificationsEnabled;
  bool? mentionNotificationsEnabled;
  String? messageNotificationSound;
  bool? showNotificationPreview; // Show message content in notifications
  
  /// Quiet hours for this identity (can override global settings)
  String? quietHoursStart; // HH:mm format
  String? quietHoursEnd;
  bool? useGlobalQuietHours; // Use app-global quiet hours instead
  
  // ========================================
  // BUSINESS/E-COMMERCE SETTINGS PER IDENTITY
  // ========================================
  
  /// Business-related settings (for business identities)
  bool? isBusinessIdentity;
  String? businessCategory;
  String? businessDescription;
  bool? showBusinessHours;
  String? businessHoursStart;
  String? businessHoursEnd;
  String? businessDaysOfWeek; // JSON array of days
  
  /// E-commerce settings for this identity
  bool? enableOrderNotifications;
  bool? enablePaymentNotifications;
  bool? enableDeliveryNotifications;
  String? preferredCurrency; // Can override global currency for business
  String? defaultShippingAddress; // Identity-specific shipping
  
  // ========================================
  // CONTACT AND BLOCKING SETTINGS
  // ========================================
  
  /// Contact management for this identity
  bool? allowContactSync; // Sync contacts for this identity
  bool? showInContactList; // Show this identity in others' contact lists
  String? contactSyncPreference; // 'all', 'mutual', 'none'
  
  /// Auto-reply settings
  bool? enableAutoReply;
  String? autoReplyMessage;
  bool? autoReplyOnlyWhenOffline;
  
  // ========================================
  // SECURITY SETTINGS PER IDENTITY
  // ========================================
  
  /// Security preferences for this identity
  bool? enableMessageEncryption; // End-to-end encryption for this identity
  bool? requireVerificationForNewChats;
  bool? enableDisappearingMessages;
  int? defaultDisappearingMessageTimer; // In seconds
  
  /// Session management
  bool? enableMultiDeviceSync; // Sync this identity across devices
  bool? logSecurityEvents; // Log security events for this identity
  
  // ========================================
  // METADATA
  // ========================================
  
  /// Settings metadata
  DateTime? lastModified;
  
  DateTime? createdAt;
  
  int? settingsVersion; // For migration purposes
  
  /// Additional custom settings as JSON
  String? customSettings;

  IdentitySettings({
    required this.myIdentityCardId,
    
    // Display defaults
    this.displayName,
    this.statusMessage,
    this.avatarUrl,
    this.customThemeForIdentity,
    
    // Privacy defaults
    this.whoCanSeeProfile,
    this.whoCanMessageMe,
    this.showLastSeen,
    this.showOnlineStatus,
    this.showProfilePhoto,
    this.showStatusMessage,
    this.sendReadReceipts,
    this.showTypingIndicators,
    this.allowForwarding,
    this.allowScreenshots,
    
    // Messaging defaults
    this.autoDownloadMedia,
    this.mediaDownloadPreference,
    this.compressImages,
    this.enableVoiceMessages,
    this.enableVideoMessages,
    this.voiceMessageQuality,
    this.chatWallpaper,
    this.chatFontSize,
    this.showTimestamps,
    this.enterToSend,
    
    // Notification defaults
    this.messageNotificationsEnabled,
    this.groupMessageNotificationsEnabled,
    this.mentionNotificationsEnabled,
    this.messageNotificationSound,
    this.showNotificationPreview,
    this.quietHoursStart,
    this.quietHoursEnd,
    this.useGlobalQuietHours,
    
    // Business defaults
    this.isBusinessIdentity,
    this.businessCategory,
    this.businessDescription,
    this.showBusinessHours,
    this.businessHoursStart,
    this.businessHoursEnd,
    this.businessDaysOfWeek,
    this.enableOrderNotifications,
    this.enablePaymentNotifications,
    this.enableDeliveryNotifications,
    this.preferredCurrency,
    this.defaultShippingAddress,
    
    // Contact defaults
    this.allowContactSync,
    this.showInContactList,
    this.contactSyncPreference,
    this.enableAutoReply,
    this.autoReplyMessage,
    this.autoReplyOnlyWhenOffline,
    
    // Security defaults
    this.enableMessageEncryption,
    this.requireVerificationForNewChats,
    this.enableDisappearingMessages,
    this.defaultDisappearingMessageTimer,
    this.enableMultiDeviceSync,
    this.logSecurityEvents,
    
    // Metadata
    this.lastModified,
    this.createdAt,
    this.settingsVersion,
    this.customSettings,
  });

  /// Create default identity settings
  factory IdentitySettings.defaults(String myIdentityCardId, {bool isBusinessIdentity = false}) {
    final now = DateTime.now();
    return IdentitySettings(
      myIdentityCardId: myIdentityCardId,
      createdAt: now,
      lastModified: now,
      settingsVersion: 1,
      
      // Privacy defaults
      whoCanSeeProfile: 'contacts',
      whoCanMessageMe: 'contacts',
      showLastSeen: true,
      showOnlineStatus: true,
      showProfilePhoto: true,
      showStatusMessage: true,
      sendReadReceipts: true,
      showTypingIndicators: true,
      allowForwarding: true,
      allowScreenshots: true,
      
      // Messaging defaults
      autoDownloadMedia: true,
      mediaDownloadPreference: 'wifi_only',
      compressImages: true,
      enableVoiceMessages: true,
      enableVideoMessages: true,
      voiceMessageQuality: 'medium',
      showTimestamps: true,
      enterToSend: false,
      
      // Notification defaults
      messageNotificationsEnabled: true,
      groupMessageNotificationsEnabled: true,
      mentionNotificationsEnabled: true,
      showNotificationPreview: true,
      useGlobalQuietHours: true,
      
      // Business defaults
      isBusinessIdentity: isBusinessIdentity,
      enableOrderNotifications: isBusinessIdentity,
      enablePaymentNotifications: isBusinessIdentity,
      enableDeliveryNotifications: isBusinessIdentity,
      
      // Contact defaults
      allowContactSync: true,
      showInContactList: true,
      contactSyncPreference: 'mutual',
      enableAutoReply: false,
      autoReplyOnlyWhenOffline: true,
      
      // Security defaults
      enableMessageEncryption: true,
      requireVerificationForNewChats: false,
      enableDisappearingMessages: false,
      enableMultiDeviceSync: true,
      logSecurityEvents: true,
    );
  }

  /// Update last modified timestamp
  void touch() {
    lastModified = DateTime.now();
  }
}