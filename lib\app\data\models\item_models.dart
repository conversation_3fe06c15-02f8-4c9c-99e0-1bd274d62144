import 'package:deewan/app/data/models/address_model.dart';
import 'package:deewan/app/data/models/identity_model.dart';
import 'package:deewan/app/data/models/medical_record_model.dart';
import 'package:deewan/app/data/models/product_model.dart';
import 'package:deewan/app/data/models/collection_model.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:isar_community/isar.dart';

part 'item_models.g.dart';

@collection
class Item {
  //listtaile view
  // product , contact card ,address ,msg ,,lap med
  Id id = Isar.autoIncrement;
  final String? itemTitle;
  final address = IsarLink<Address>(); //
  final identity = IsarLink<Identity>(); // id cards
  final product = IsarLink<Product>(); //
  final medicalRecord = IsarLink<MedicalRecord>(); // lap or med records
  //final linkPreview = IsarLink<LinkPreview>();
  @Backlink(to: 'item')
  final itemJoins = IsarLinks<ItemJoin>();
  
  final collections = IsarLinks<CollectionModel>();

  final images = IsarLinks<ImageUrl>();

  Item({this.itemTitle});
}

@collection
class ItemJoin {
  Id id = Isar.autoIncrement;
  final item = IsarLink<Item>(); // join attribute
  final itemlist = IsarLink<ItemList>(); // join attribute
  final addedBy = IsarLink<Identity>();
  final DateTime addedAt;
  final int? quantity;
  final String? statusInList;
  final String? note;
   
  ItemJoin({required this.addedAt, this.statusInList, this.note, this.quantity});
}

@collection
class ItemList {
  //the package class
  Id id = Isar.autoIncrement;
  final String itemListID;
  final String? title;
  final int? status; // ready ongoing, cancelled, deleted ,
  final String? description; //, cutions , glass , fresh , hot,list of permittes
  final createdBy = IsarLink<Identity>();
  final DateTime? createdAt;
  final DateTime? lastUpdate;
  final bool? isFulfilled;
  final fulfilledBy = IsarLink<Identity>();
  @Backlink(to: 'itemlist')
  final itemJoins = IsarLinks<ItemJoin>();
  final int? itemsCount;
  final double? total; // price , results ,
  final int type;
  final List<String>? tags = [];
       //type of list ,should be a unified type of items : ID_list or shopping_cart or address or Saved_messages or medical_records
  // @Backlink('store')
  // final contactPage = IsarLink<ContactPage>();
  // final orderRoom = IsarLink<Room>();
   //accessScope (String/enum): (e.g., "PRIVATE", "SHARED_READ_ONLY", "SHARED_READ_WRITE", "ORGANIZATION_VISIBLE", "PUBLIC"). Controls visibility and collaboration
  //version (int): For optimistic locking or tracking changes if needed.
  //isTemplate (bool): Indicates if this list can be used as a template to create new lists.

  //final myIdentity = IsarLink<MyIdentity>();
  ItemList({
      this.isFulfilled,
      this.createdAt,
      this.itemsCount,
      this.total,
      required this.type,
      this.title,
      required this.itemListID,
      this.status,
      this.lastUpdate,
      this.description
  });
}
