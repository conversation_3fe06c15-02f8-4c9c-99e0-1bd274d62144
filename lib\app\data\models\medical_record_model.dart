import 'package:deewan/app/data/models/identity_model.dart';
import 'package:deewan/app/data/models/item_models.dart';
import 'package:isar_community/isar.dart';

part 'medical_record_model.g.dart';

@collection
class MedicalRecord {
  Id id = Isar.autoIncrement;
  
  final String recordId; //lap or visit note or mhx or dx or sx or med or lab or referral or procedure or plan or instruction or report
  final String recordTitle;
  final identity = IsarLink<Identity>();
  final itemId = IsarLink<Item>();
  final String type; // lap , visit note , mhx , shx , hpi , dx , sx , med , lab , referral , procedure , plan , instruction , report , etc
  final String? content; // content or result  depends on the type of medical records
  final String? status; //ready , ongoing , cancelled , deleted , archived
  final DateTime? addAt;
  final DateTime? updatedAt;
  final String description;
  
  @Backlink(to: 'medicalRecord')
  final item = IsarLinks<Item>();
  
  MedicalRecord({
    required this.recordId,
    required this.recordTitle,
    this.updatedAt,
    this.status,
    this.addAt,
    required this.description,
    required this.type,
    this.content,
  });
}