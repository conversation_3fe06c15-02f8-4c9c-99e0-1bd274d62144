import 'package:deewan/app/data/models/media_model.dart';
import 'package:isar_community/isar.dart';
import 'room_model.dart';
import 'package:deewan/core/enums/enums.dart';
part 'message_models.g.dart';

@collection
class Message {
  Id id = Isar.autoIncrement;
  final int messageId;
  final IsarLink<Room> room = IsarLink<Room>();
  final String senderId;
  final DateTime creationtimestamp;
  final bool? isSent;
  final bool? isEdited;
  final DateTime? editedTimestamp;
  final String? starType;
  final bool? isSeen;
  final DateTime? deliveredTimestamp;
  final DateTime? sentTimestamp;
  final DateTime? seenTimestamp;
  final String? messageContent;
  @Index() // Index for efficient chronological queries
  final DateTime? creationTimestamp;

  final images = IsarLinks<ImageUrl>();

  bool? isFromMe(String currentUserId) {
    return senderId == currentUserId;
  }

  bool isModified() => isEdited ?? false;
  DateTime getLastUpdateTime() => editedTimestamp ?? creationtimestamp;

  Message({
    this.starType,
    this.isSent,
    this.isEdited,
    this.editedTimestamp,
    this.isSeen,
    this.deliveredTimestamp,
    this.sentTimestamp,
    this.seenTimestamp,
    this.messageContent,
    this.creationTimestamp,
    required this.senderId,
    required this.messageId,
    required this.creationtimestamp,
  });
}




