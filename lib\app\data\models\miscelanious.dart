// enum class for each لكي تكون الادخالات فقط المسجلة في التطبيق
enum PTS {
  unkown,
  size,
  sizePattern, // decreasing or increasing
  count,
  amount,
  location,
  shape,
  temperature,
  smell,
  radiation,
  color,
  consistensy,
  content2,
  weight,
  symetricity,
  onset,
  course,
  durationQl,
  durationQn,
  frequency,
  discription,
  progression,
  aggravatingfactors,
  alleviatingfactors,
  associatedsympt,
  intensity,
  precipitatingfactor,
  quality,
}

// abstract mixin class for each التحكم في الاعراض والامراض
//المدخلة المسجلة فقط في التطبيق
enum SX {
  knownCaseOf,//dx
  unkown,
  dizziness,
  convulsion,
  unconscious,
  syncope,
  constipation,
  cough,
  deafness,
  sight,
  visual,
  diarrhoea,
  dysphagia,
  dyspnoea,
  fatigue,
  fever,
  headache,
  insomnia,
  nightsweats,
  sweating,
  weight_gain_loss,
  muscle_weakness,
  incontinence,
  retention, //feacal /urinary
  gait_abnormality,
  emesis,
  nuasea,
  vomiting,
  heamoptysis,
  tremor,
  hallucination,
  hiccups,
  hirsutism,
  impotence,
  oliguria,
  polyuria,
  palpitation,
  voice_pharynx,
  thirst,
  tiredness,
  pain,
  swelling,
  ulcer,
  mass,
  lump,
  forigen_body,
  color,
  rash,
  burn,
  perepheral_deformities,
  plegia, // limb_bulbar,
  discharge,
  bleeding,
  pruritis,
  hearing,
  tactile,
  taste,
  smell,
  breath,
  feeling,
  mood,
  tinnitis, //
  edema,
  dyspnea,
  hair_loss,
  sore_throat,
  jaundice,
  anorectal,
  flank_pain, //??
  amenorrhea,
  breast_complaints,
  diplopia,
  confusion,
  anxiety,
  panic_attack,
}

// كل صفقة لقطاع ما له ملفاته الخاصة
enum HealthcareFiles {
  // انواع الملفات الرئيسية والتي تظهر تحت السليفر بار
  // elements of level 4 & exist in mongo as is
  mhx, // تشمل كل التاريخ المرضي الجراحي والباطني الخ الخ
  dx, // confirmed or  known case list of dx
  hpi, //???
  lab, // منتجات لكن هنا كبيانات حيوية للمريض
  med, // list of drugs هي منتجات عند الشراء لكن تخزن هنا عند الاستعمال اي بدون خانة السعر
  biometrics, // age group , ethnicity , biometrics ,hight wight etc
  screening, // questainnares / age group related or  ddx,dx triage related
  // النماذج ايضا منتجات تتداول ولكن الاجوبة هي بيانات تخزن هنا
  medicalreports, // admission , referral , towhomconcern,case confirmation ,consents???
  reasarch, //???? enrolled init to recieve notific .
  nickname, // optional?  تنظيمية   ملفات
 // from mongodb
   // specific to each file ,list of map who can access it and when and why
}
//ليس بالضرورة ان كل الاقسام هي عبارة عن نوع صقفقة خاصة بالملف الصحي
// لان حجز المواعيد عن طريق صفقة مع السكرتارية وليس فيها
//triage model بل اتفاق مع السكرتير بصفقة ليست طبية

// ignore_for_file: public_member_api_docs, sort_constructors_first

//--------------------------------------------------------------------

// lab == service => fill data into the deal object
//procedure == service
//plan == service
//instruction  ارشادات== service
//
//, med == products => assets of the deal
// ,sx == service => fill data from user\merchant\ django func server,
// @JsonSerializable()
//level 2==>lab med hpi mhx dx syx age_group are simple list \ headache,dizziness, db,ht,acd,ckd
// level 1==>) are complex form( quest+ lab med syx list)
// تمكين انشاء نماذج ادخال من جهات مختلفة \نماذج حسب الطلب من قبل الطبيب\مؤسسة
//ckd ht cad / na k ca rash dysuria اسم
// خاص بالعنصر نفسه /معرف في كلاس الموديل
// مكونات الثانوية للعنصر /خياراته/ خاصة بالسؤال

class SxModel {
  // to store sxname components to show in the sxsheet before filling the sxsheet for each sx
  final int id;
  final SX sxname;
  final String info;
  final List<PTS> components; // to be listed thru ptsmodel in the sxsheet
  SxModel(this.id, this.sxname, this.components, this.info);
}

class PTSModel {
  final PTS header;
  final String info;
  final String type; // type of card to be viewed
  final List<dynamic> contents; // <Choices>
  PTSModel(
    this.contents,
    this.info, {
    required this.header,
    required this.type,
  });
}

class SxCartModel {
  //??????
  // العناصر المكونة للطلب
  // كل الكلاسات الباقية سوف تاخذ منه وهي توازي السلعة في تطبيق المتجر
  final String createdby;
  final String itemname;
  final String type; // sx list of sx, syndrom , dx , hpi ,mhx ,shx ,allerg etc
  final bool? responsestatus;
  final String answer;
  final List<PTSModel>? size,
      count,
      amount,
      location,
      shape,
      temperature,
      smell,
      radiation, // radiation to \ from
      color,
      consistensy,
      content,
      weight,
      symetricity,
      onset,
      course,
      duration,
      frequency,
      discription,
      progression,
      aggravatingfactors,
      alleviatingfactors,
      associatedsympt,
      intensity,
      precipitatingfactor,
      quality,
      itemcomponents; // these are the spicial components of the sx espicialy if its a form or study
  final String? pathognomonic_value;

  SxCartModel(
      this.createdby,
      this.itemname,
      this.type,
      this.responsestatus,
      this.answer,
      this.size,
      this.count,
      this.amount,
      this.location,
      this.shape,
      this.temperature,
      this.smell,
      this.radiation,
      this.color,
      this.consistensy,
      this.content,
      this.weight,
      this.symetricity,
      this.onset,
      this.course,
      this.duration,
      this.frequency,
      this.discription,
      this.progression,
      this.aggravatingfactors,
      this.alleviatingfactors,
      this.associatedsympt,
      this.intensity,
      this.precipitatingfactor,
      this.quality,
      this.itemcomponents,
      this.pathognomonic_value);
}

// الاسئلة الثابتة في ملف constant

// class Appointments { // extends service

// }
// class Procedure { // extends service

// }
// class Referral {// extends service

// }

// class Instruction { //send webpage info to the client
// this is deals file, all services are deal file

// }
