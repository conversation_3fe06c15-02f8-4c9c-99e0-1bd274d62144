import 'package:deewan/app/data/models/item_models.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:isar_community/isar.dart';

part 'product_model.g.dart';

@collection
class Product {
  Id id = Isar.autoIncrement;
  final String? productId;
  final String? productTitle;
  
  @Backlink(to: 'product')
  final item = IsarLinks<Item>();
  
  final String? productStatus; // ready ongoing, cancelled, deleted ,
  final createdBy = IsarLink<Item>();
  final DateTime? lastUpdate; // very important to know when the package was last updated
  late String? productDescription;
  @Index(type: IndexType.value, caseSensitive: false)
  List<String> get productDescriptionWords => Isar.splitWords(productDescription!);
  final String? productBarcode;
  final String? productSku;
  final String? productSerialCode;
  final double? price;
  final int? count;
  final bool? isAvailable;
  final String? productResult;

  Product({
      this.productId,
      this.productTitle,
      this.productStatus,
      this.lastUpdate,
      this.productDescription,
      this.productBarcode,
      this.productSku,
      this.productSerialCode,
      this.price,
      this.count,
      this.isAvailable,
      this.productResult,
  });
}

@collection
class Collection {
  Id id = Isar.autoIncrement;
  late  String? collectionId;
  late  String? collectionTitle;
  late  String? collectionDescription;
  @Index(type: IndexType.value, caseSensitive: false)
  List<String> get collectionDescriptionWords => Isar.splitWords(collectionDescription!);
  late  ImageUrl? collectionImage;
  late  String? storeIdentityId;
  late  List<String>? productIds;
}