import 'package:deewan/app/data/models/identity_model.dart';
import 'package:deewan/app/data/models/media_model.dart';
import 'package:deewan/core/enums/enums.dart';
import 'package:isar_community/isar.dart';
import 'package:deewan/app/data/models/message_models.dart';

part 'room_model.g.dart';

 @collection
 class Room {
    Id id = Isar.autoIncrement;
    @Index(unique: true, replace: true)
  late  String roomId;
    /// Room type discriminator (public, private, order)
  @enumerated
  late RoomType type;
    /// One-to-one link to room type, only one of the three should be set
  final IsarLink<PrivateRoom>? privateData = IsarLink<PrivateRoom>();
  final IsarLink<PublicRoom>? publicData = IsarLink<PublicRoom>();
  final IsarLink<OrderRoom>? orderData = IsarLink<OrderRoom>(); 
  
  late ImageUrl? imageUrl;
/// messages
 @Backlink(to: 'room')
  final IsarLinks<Message> messages = IsarLinks<Message>();
/// participants
  @Backlink(to: 'room')
  final IsarLinks<RoomMembership>? participants=IsarLinks();
  @Backlink(to: 'room')
  final IsarLinks<Participant>? roomMemberships=IsarLinks();
/// property specific for the this user in this room
  // late List<Message> pinnedMessages = [];
  late RoomStatus? status;
  late bool? isArchived;
  late bool? isMuted;
  late bool? isPinned;
  late bool? isStarred;
  late bool? isHidden;
  late bool? isBlocked;
  late bool? isRead;
  late DateTime? lastMessageTimestamp;
  late String? lastMessageContent;
  late String? lastMessageAuthor;
  late DeliveryStatus? lastMessageDeliveryStatus;
  late StarType? lastMessageStarType;
  late int? unreadMessagesCount;

  Room({
    required this.roomId,
    this.status,
    this.isArchived,
    this.isMuted,
    this.isPinned,
    this.isStarred,
    this.isHidden,
    this.isBlocked,
    this.isRead,
    this.lastMessageTimestamp,
    this.lastMessageContent,
    this.lastMessageAuthor,
    this.lastMessageDeliveryStatus,
    this.lastMessageStarType,
    this.unreadMessagesCount,
  });
}

@collection
class PrivateRoom {
  Id id = Isar.autoIncrement;
  /// Link back to base room
  final IsarLink<Room> room = IsarLink<Room>();
    // Private room–specific data could go here
}

@collection
class PublicRoom  {
  Id id = Isar.autoIncrement;
  final IsarLink<Room> room = IsarLink<Room>();
  late String? title;// channel name
  late String? description;
  PublicRoom({
      this.title,
      this.description,
  }) ;
}

@collection
class OrderRoom  {
  Id id = Isar.autoIncrement;
  final IsarLink<Room> room = IsarLink<Room>();
  late String orderTitle;
  @Enumerated(EnumType.name)
  late OrderCategory orderCategory;
  @Enumerated(EnumType.name)
  late OrderStatus orderStatus;
  late DateTime orderDate;

  OrderRoom({
      required this.orderTitle,
      required this.orderCategory,
      required this.orderDate,
      required this.orderStatus,
  }) ;
}

@embedded
class Participant {
  Id id = Isar.autoIncrement;
  final room = IsarLink<Room>();              // canonical room membership
  final identity = IsarLink<Identity>();      // who the participant is
  @Enumerated(EnumType.name)
  ParticipantRole? role;                      // owner, admin, member, guest
  DateTime? joinedAt;
  DateTime? leftAt;                           // optional: if they left
  bool? isBanned;
  // Server authoritative flags (do not store client-only preferences here)
  Map<String, String>? serverMetadata;
  Participant();
}

@collection
class RoomMembership {
  Id id = Isar.autoIncrement;
  final room = IsarLink<Room>();
  final identity = IsarLink<Identity>();      // the local identity this membership belongs to

  // read / sync state
  String? lastReadMessageId;
  DateTime? lastReadAt;
  DateTime? lastSyncAt;    // when the client last synced this room for this identity
  int unreadCount = 0;

  // user preferences / UI state
  bool isMuted = false;
  DateTime? muteExpiresAt;
  bool isPinned = false;
  int? pinOrder;
  bool isStarred = false;
  bool isHidden = false;     // hidden from lists
  bool isArchived = false;
  String? customTitle;       // per-user room name
  String? draftText;
  List<int>? draftAttachmentIds;  // local attachment ids
  List<int>? pinnedMessageIds;    // message IDs pinned by this user

  // cached last message for dashboard performance
  String? lastMessageId;
  String? lastMessageContent;
  DateTime? lastMessageTimestamp;
  String? lastMessageAuthor;

  RoomMembership();
}