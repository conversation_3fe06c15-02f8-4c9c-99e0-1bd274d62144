import 'package:deewan/app/data/models/cart_model.dart';
import 'package:deewan/app/data/models/collection_model.dart';
import 'package:deewan/app/data/models/identity_model.dart';
import 'package:isar_community/isar.dart';

part 'store_page_model.g.dart';

@collection
class StorePage {
  Id id = Isar.autoIncrement;
  final String storePageId; // Unique identifier
  final String? name;
  final String? description;
  final String? workingHours;
  final String? address;
  final String? phoneNumber;
  final String? email;
  final String? website;
  final bool isActive;
  
  // One-to-one relationship with Identity (store identities only)
  final storeIdentity = IsarLink<Identity>();
  
  // Store-specific collections
  @Backlink(to: 'storePage')
  final collections = IsarLinks<CollectionModel>();
  
  // Template cart for customers to clone
  final templateCart = IsarLink<Cart>();
  
  // All carts associated with this store (customer carts + template)
  @Backlink(to: 'store')
  final associatedCarts = IsarLinks<Cart>();
  final DateTime createdAt;
  final DateTime updatedAt;
  StorePage({
    required this.storePageId,
    this.name,
    this.description,
    this.workingHours,
    this.address,
    this.phoneNumber,
    this.email,
    this.website,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });
}

@collection
class StoreParticipant {
  Id id = Isar.autoIncrement;
  
  final storeIdentity = IsarLink<Identity>(); // Store identity
  final participantIdentity = IsarLink<Identity>(); // Private identity managing the store
  final String role; // 'owner', 'manager', 'staff', 'viewer'
  final bool isActive;
  
  final DateTime joinedAt;
  
  DateTime? lastActiveAt;

  StoreParticipant({
    required this.role,
    this.isActive = true,
    required this.joinedAt,
    this.lastActiveAt,
  });
}