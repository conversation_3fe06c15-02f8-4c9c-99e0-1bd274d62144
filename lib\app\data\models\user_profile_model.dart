import 'package:deewan/app/data/models/identity_model.dart';
import 'package:isar_community/isar.dart';
import 'package:deewan/app/data/models/media_model.dart';

part 'user_profile_model.g.dart';

@collection
class UserPofile {
  Id id = Isar.autoIncrement;
  final String userProfileId;
  final String? title;
  //final registeredDeviceIds = IsarLinks<Device>();
  final String? email;
  final String? phone;
  final String? pinCode;
  final bool? isVerified;
  final bool emailVerified;
  final bool phoneVerified;

  @Backlink(to: 'imageUrl')
  final circleAvatar = IsarLinks<ImageUrl>();

  @Backlink(to: 'userProfile')
  final myIdentity = IsarLinks<MyIdentity>();

  final DateTime createdAt;

  UserPofile({
    required this.userProfileId,
    this.title,
    this.email,
    this.phone,
    this.pinCode,
    this.isVerified,
    required this.emailVerified,
    required this.phoneVerified,
    required this.createdAt,
  });
}
