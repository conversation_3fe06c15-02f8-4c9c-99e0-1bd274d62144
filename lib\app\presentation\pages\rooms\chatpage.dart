// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:deewan/app/presentation/widgets/messageBubble.dart';
import 'package:flutter/material.dart';
import 'package:deewan/app/data/models/message_models.dart';

// ignore: must_be_immutable
class ChatPage extends StatelessWidget {
  final Message messages = controller.messages;
  ChatPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("auther.userName"),
      ),
      body:
          
       CustomScrollView(
        slivers: [
          // SliverList for displaying chat messages
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final message = messages[index];
                return MessageBubble(
                    
                    message: message.messageContent,
                    currentAccount: "currentAccount",
                    auther: "auther");
              },
              childCount: messages.length,
            ),
          ),
          // Optional: Add a spacer to push the chat content towards the bottom
          SliverToBoxAdapter(
            child: SizedB<PERSON>(height: MediaQuery.of(context).padding.bottom),
          ),
        ],
      ),
      // Input field for sending messages
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                  decoration:
                      const InputDecoration(hintText: 'Type your message'),
                  onSubmitted: (text) => ()),
            ),
            IconButton(
              onPressed: () => (), // Replace with your logic
              icon: const Icon(Icons.send),
            ),
          ],
        ),
      ),
    );
  }
}
