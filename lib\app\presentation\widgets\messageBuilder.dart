import 'package:deewan/app/data/models/message_models.dart';
import 'package:flutter/material.dart';

class BuildMessage extends StatelessWidget {
  final Message message;
  const BuildMessage(this.message, {super.key});
  @override
  Widget build(BuildContext context) {
    final messageBuilder = <Messagetype, Widget Function(Message)>{
      Messagetype.text: (m) => Text(m.messageContent),
      Messagetype.image: (m) => Image.network(m.messageContent,
          height: 200, width: 200, fit: BoxFit.cover),
      Messagetype.audio: (m) => const Text(
          " AudioPlayer(message)"), // Assuming you have an AudioPlayer widget
      Messagetype.link: (m) => TextButton(
            onPressed: () => {}, // Replace with your logic
            child: Text(m.messageContent),
             
          ),
    };

    return messageBuilder[message.messageType]!(message);
  }
}
