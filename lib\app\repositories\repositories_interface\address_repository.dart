import 'dart:async';
import 'package:deewan/app/data/models/address_model.dart';

abstract class AddressRepositoryInterface {
  Stream<List<Address>> watchUserAddresses(String userProfileId);
  Stream<Address?> watchAddress(String addressId);
  Stream<Address?> watchDefaultAddress(String userProfileId);
  
  Future<List<Address>> getUserAddresses(String userProfileId);
  Future<Address?> getAddressById(String addressId);
  Future<Address?> getDefaultAddress(String userProfileId);
  Future<List<Address>> getAddressesByType(String userProfileId, AddressType type);
  
  Future<void> saveAddress(Address address);
  Future<void> updateAddress(Address address);
  Future<void> deleteAddress(String addressId);
  Future<void> setDefaultAddress(String addressId, String userProfileId);
  
  Future<List<Address>> searchAddresses(String query, {String? userProfileId});
  Future<List<Address>> getAddressesNearLocation(double latitude, double longitude, 
      double radiusKm, {String? userProfileId});
  Future<void> validateAddress(Address address);
}
