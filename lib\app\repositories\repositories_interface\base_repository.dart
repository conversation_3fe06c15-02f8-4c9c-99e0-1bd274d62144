import 'package:dartz/dartz.dart';
import 'package:deewan/core/errors/failures.dart';
import 'package:deewan/core/errors/error_handler.dart';
import 'package:deewan/core/network/api_client.dart';
import 'package:deewan/core/storage/secure_storage.dart';
import 'package:deewan/app/data/dto/api_response_models.dart';

/// Base repository class that all repositories should extend
abstract class BaseRepository {
  final ApiClient apiClient;
  final SecureStorage secureStorage;

  BaseRepository({required this.apiClient, required this.secureStorage});

  /// Generic method to handle API calls
  Future<Either<Failure, T>> handleApiCall<T>(
    Future<T> Function() apiCall,
  ) async {
    return await ErrorHandler.handleAsyncOperation(apiCall);
  }

  /// Generic method to handle cache operations
  Either<Failure, T> handleCacheOperation<T>(T Function() cacheOperation) {
    return ErrorHandler.handleSyncOperation(cacheOperation);
  }

  /// Get authentication token
  Future<String?> getAuthToken() async {
    try {
      return await secureStorage.read('auth_token');
    } catch (e) {
      return null;
    }
  }

  /// Set authentication token
  Future<void> setAuthToken(String token) async {
    await secureStorage.write('auth_token', token);
  }

  /// Remove authentication token
  Future<void> removeAuthToken() async {
    await secureStorage.delete('auth_token');
  }

  /// Get headers with authentication
  Future<Map<String, String>> getAuthHeaders() async {
    final token = await getAuthToken();
    final headers = <String, String>{'Content-Type': 'application/json'};

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }
}

/// Generic CRUD repository interface
abstract class CrudRepository<T> extends BaseRepository {
  CrudRepository({required super.apiClient, required super.secureStorage});

  /// Get all items
  Future<Either<Failure, BaseListResponse<T>>> getAll({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  });

  /// Get item by ID
  Future<Either<Failure, T>> getById(String id);

  /// Create new item
  Future<Either<Failure, T>> create(Map<String, dynamic> data);

  /// Update existing item
  Future<Either<Failure, T>> update(String id, Map<String, dynamic> data);

  /// Delete item
  Future<Either<Failure, bool>> delete(String id);

  /// Search items
  Future<Either<Failure, BaseListResponse<T>>> search({
    required String query,
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  });
}

// Example implementation would go here
// Concrete repositories should extend CrudRepository<YourEntityType>
// For example:
// class UserRepository extends CrudRepository<UserPofile> { ... }
