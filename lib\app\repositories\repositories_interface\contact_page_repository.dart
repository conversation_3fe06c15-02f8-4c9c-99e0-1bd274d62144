import 'dart:async';
import 'package:deewan/app/data/models/contact_page_model.dart';

abstract class ContactPageRepositoryInterface {
  Stream<ContactPage?> watchContactPage(String identityId);
  Stream<List<ContactPage>> watchPublicContactPages();
  
  Future<ContactPage?> getContactPageByIdentityId(String identityId);
  Future<ContactPage?> getContactPageById(String contactPageId);
  Future<List<ContactPage>> getPublicContactPages({int limit = 20, int offset = 0});
  Future<List<ContactPage>> searchContactPages(String query);
  
  Future<void> saveContactPage(ContactPage contactPage);
  Future<void> updateContactPage(ContactPage contactPage);
  Future<void> deleteContactPage(String contactPageId);
  
  Future<void> updatePageVisibility(String contactPageId, bool isPublic);
  Future<void> updateBusinessInfo(String contactPageId, String? businessType, 
      String? businessDescription, String? website);
  Future<void> addSocialMediaLink(String contactPageId, String platform, String url);
  Future<void> removeSocialMediaLink(String contactPageId, String platform);
}
