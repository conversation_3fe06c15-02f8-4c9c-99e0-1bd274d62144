


import 'package:deewan/app/data/models/identity_model.dart';

abstract class ContactSyncRepository {
  Future<List<PhoneContact>> getPhoneContacts();
  Future<bool> syncPhoneContacts();
  Future<List<Identity>> findRegisteredContacts(List<String> phoneNumbers);
  Future<bool> inviteContact(String phoneNumber);
  Stream<ContactSyncStatus> get syncStatusStream;
  Future<DateTime?> getLastContactSyncTime();
  Future<void> setContactSyncEnabled(bool enabled);
  Future<bool> isContactSyncEnabled();
  Future<void> clearSyncedContacts();
}
