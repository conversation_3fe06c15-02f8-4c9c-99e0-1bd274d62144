// repositories/abstracts/room_repository_interface.dart
import 'dart:async';
import 'package:deewan/core/utils/classes/enums.dart';
import 'package:deewan/app/data/models/message_models.dart';
import 'package:deewan/app/data/models/identity_model.dart';

abstract class RoomRepositoryAbstract {
  Future<List<Room>> getMyRooms(String myIdentityId);
  Stream<List<Room>> watchMyRooms(String myIdentityId);//maybe redundunt
  Stream<List<Room>> getArchivedRooms(String myIdentityId);
  Stream<Room?> watchRoom(String roomId);
  Future<Room> createPrivateRoom(List<Identity> participantId);
  Future<Room> createPublicChannelRoom(String title, String creatorId, {String? description});
  Future<Room> createOrderRoom(String title, String creatorId, OrderType orderType);
  Future<Room?> getRoomById(String roomId);
  Future<bool> updateRoom(Room room);
  Future<bool> deleteRoom(String roomId);
  Future<bool> archiveRoom(String roomId);
  Future<bool> unarchiveRoom(String roomId);
  Future<bool> muteRoom(String roomId);
  Future<bool> unmuteRoom(String roomId);
  Future<bool> pinRoom(String roomId);
  Future<bool> unpinRoom(String roomId);
  Future<bool> markRoomAsRead(String roomId);

  Future<bool> addParticipant(String roomId, String identityId);
  Future<bool> removeParticipant(String roomId, String identityId);
  Future<List<RoomParticipant>> getRoomParticipants(String roomId);
  Future<void> updateParticipantRole(String roomId, String identityId, String role);
  Future<void> updateParticipantTitle(String roomId, String identityId, String title);
  Future<List<Room>> searchRooms(String query);//??
}
