import 'app_services_interface.dart';

/// Interface for analytics service - handles app analytics and tracking
abstract class AnalyticsServiceAbstract extends InitializableServiceAbstract {
  Future<void> logEvent(String eventName, Map<String, dynamic> parameters);
  Future<void> setUserId(String userId);
  Future<void> setUserProperty(String name, String value);
  Future<void> logScreenView(String screenName);
  Future<void> logError(Exception error, StackTrace stackTrace);
  Future<void> logCustomEvent(CustomAnalyticsEvent event);
  Future<void> startSession();
  Future<void> endSession();
  Future<void> enableAnalytics(bool enabled);
  void dispose();
}

// Supporting classes
enum EventType { user_action, system_event, error, performance, custom }

class CustomAnalyticsEvent {
  final String name;
  final EventType type;
  final Map<String, dynamic> parameters;
  final DateTime timestamp;
  final String? userId;
  final String? sessionId;
  
  CustomAnalyticsEvent({
    required this.name,
    required this.type,
    required this.parameters,
    required this.timestamp,
    this.userId,
    this.sessionId,
  });
}

class AnalyticsSession {
  final String id;
  final DateTime startTime;
  final DateTime? endTime;
  final String? userId;
  final Map<String, dynamic> properties;
  
  AnalyticsSession({
    required this.id,
    required this.startTime,
    this.endTime,
    this.userId,
    this.properties = const {},
  });
}

class ErrorReport {
  final String id;
  final Exception error;
  final StackTrace stackTrace;
  final DateTime timestamp;
  final String? userId;
  final String? screenName;
  final Map<String, dynamic> context;
  
  ErrorReport({
    required this.id,
    required this.error,
    required this.stackTrace,
    required this.timestamp,
    this.userId,
    this.screenName,
    this.context = const {},
  });
}
