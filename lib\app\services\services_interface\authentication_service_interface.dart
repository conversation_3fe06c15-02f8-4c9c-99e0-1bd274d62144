import 'app_services_interface.dart';

/// Interface for authentication service - handles user authentication and authorization
abstract class AuthenticationServiceAbstract extends InitializableServiceAbstract {
  Future<AuthResult> login(String email, String password);
  Future<AuthResult> register(String email, String password, String phone);
  Future<AuthResult> loginWithPhone(String phoneNumber);
  Future<AuthResult> verifyPhoneOtp(String otp);
  Future<AuthResult> loginWithBiometrics();
  Future<bool> logout();
  Future<bool> refreshToken();
  Future<UserProfile?> getCurrentUser();
  Stream<AuthStatus> get authStatusStream;
  bool get isAuthenticated;
  Future<bool> resetPassword(String email);
  Future<bool> changePassword(String oldPassword, String newPassword);
  Future<bool> deleteAccount();
  Future<void> clearAuthData();
}

// Supporting classes
enum AuthStatus { authenticated, unauthenticated, loading, error }

class AuthResult {
  final bool success;
  final String? error;
  final UserProfile? user;
  final String? token;
  AuthResult({required this.success, this.error, this.user, this.token});
}

class UserProfile {
  final String id;
  final String email;
  final String? phone;
  final String? displayName;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  
  UserProfile({
    required this.id,
    required this.email,
    this.phone,
    this.displayName,
    this.avatarUrl,
    required this.createdAt,
    required this.lastLoginAt,
  });
}
