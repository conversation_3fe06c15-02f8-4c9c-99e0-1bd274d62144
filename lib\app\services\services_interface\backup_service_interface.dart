import 'app_services_interface.dart';

/// Interface for backup service - handles data backup and restore operations
abstract class BackupServiceAbstract extends InitializableServiceAbstract {
  Future<String> createBackup({bool includeMedia = true});
  Future<void> restoreBackup(String backupPath);
  Future<void> uploadBackupToCloud(String backupPath);
  Future<String?> downloadBackupFromCloud(String backupId);
  Future<List<BackupInfo>> getCloudBackups();
  Future<void> deleteCloudBackup(String backupId);
  Future<void> scheduleAutoBackup(BackupFrequency frequency);
  Future<void> cancelAutoBackup();
  Stream<BackupProgress> get backupProgressStream;
  void dispose();
}

// Supporting classes
enum BackupFrequency { daily, weekly, monthly, never }

enum BackupStatus { idle, creating, uploading, downloading, restoring, completed, error }

class BackupInfo {
  final String id;
  final String name;
  final DateTime createdAt;
  final int size;
  final bool isCloud;
  final bool includesMedia;
  final String? description;
  final Map<String, dynamic>? metadata;
  
  BackupInfo({
    required this.id,
    required this.name,
    required this.createdAt,
    required this.size,
    this.isCloud = false,
    this.includesMedia = true,
    this.description,
    this.metadata,
  });
}

class BackupProgress {
  final BackupStatus status;
  final double progress;
  final String? currentItem;
  final String? error;
  final int? totalItems;
  final int? completedItems;
  final DateTime timestamp;
  
  BackupProgress({
    required this.status,
    this.progress = 0.0,
    this.currentItem,
    this.error,
    this.totalItems,
    this.completedItems,
    required this.timestamp,
  });
}

class BackupConfiguration {
  final BackupFrequency frequency;
  final bool includeMedia;
  final bool autoUploadToCloud;
  final int maxBackupsToKeep;
  final List<String> excludedFolders;
  
  BackupConfiguration({
    required this.frequency,
    this.includeMedia = true,
    this.autoUploadToCloud = false,
    this.maxBackupsToKeep = 5,
    this.excludedFolders = const [],
  });
}
