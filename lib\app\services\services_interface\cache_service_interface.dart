import 'app_services_interface.dart';

/// Interface for cache service - handles data caching operations
abstract class CacheServiceAbstract extends InitializableServiceAbstract {
  Future<void> put<T>(String key, T value, {Duration? expiry});
  Future<T?> get<T>(String key);
  Future<void> remove(String key);
  Future<void> clear();
  Future<void> clearExpired();
  Future<int> getCacheSize();
  Future<void> setMaxSize(int maxSizeInBytes);
  Future<void> preloadCache(List<String> keys);
  Stream<CacheEvent> get cacheEventStream;
  void dispose();
}

// Supporting classes
enum CacheEventType { put, get, remove, clear, expired }

class CacheEvent {
  final CacheEventType type;
  final String key;
  final dynamic value;
  final DateTime timestamp;
  final String? error;
  
  CacheEvent({
    required this.type,
    required this.key,
    this.value,
    required this.timestamp,
    this.error,
  });
}

class CacheEntry<T> {
  final String key;
  final T value;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final int accessCount;
  final DateTime lastAccessedAt;
  
  CacheEntry({
    required this.key,
    required this.value,
    required this.createdAt,
    this.expiresAt,
    this.accessCount = 0,
    required this.lastAccessedAt,
  });
}

class CacheStats {
  final int totalEntries;
  final int totalSize;
  final int hitCount;
  final int missCount;
  final double hitRate;
  final DateTime lastCleanup;
  
  CacheStats({
    required this.totalEntries,
    required this.totalSize,
    required this.hitCount,
    required this.missCount,
    required this.hitRate,
    required this.lastCleanup,
  });
}
