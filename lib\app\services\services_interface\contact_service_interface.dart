import 'app_services_interface.dart';

/// Interface for contact service - handles contact management
abstract class ContactServiceAbstract extends InitializableServiceAbstract {
  Future<List<PhoneContact>> getPhoneContacts();
  Future<void> syncContacts();
  Future<List<Identity>> findRegisteredContacts();
  Future<void> inviteContact(String phoneNumber);
  Future<void> blockContact(String identityId);
  Future<void> unblockContact(String identityId);
  Future<List<Identity>> getBlockedContacts();
  Future<void> addToFavorites(String identityId);
  Future<void> removeFromFavorites(String identityId);
  Stream<ContactSyncStatus> get syncStatusStream;
  void dispose();
}

// Supporting classes
enum ContactSyncStatus { idle, syncing, completed, error }

class PhoneContact {
  final String name;
  final String phoneNumber;
  final String? email;
  final String? avatarUrl;
  final List<String> additionalNumbers;
  
  PhoneContact({
    required this.name,
    required this.phoneNumber,
    this.email,
    this.avatarUrl,
    this.additionalNumbers = const [],
  });
}

class Identity {
  final String id;
  final String phoneNumber;
  final String? email;
  final String? displayName;
  final String? avatarUrl;
  final bool isRegistered;
  final bool isBlocked;
  final bool isFavorite;
  final DateTime? lastSeen;
  final String? status;
  
  Identity({
    required this.id,
    required this.phoneNumber,
    this.email,
    this.displayName,
    this.avatarUrl,
    this.isRegistered = false,
    this.isBlocked = false,
    this.isFavorite = false,
    this.lastSeen,
    this.status,
  });
}

class ContactInvitation {
  final String id;
  final String phoneNumber;
  final DateTime sentAt;
  final bool isAccepted;
  final DateTime? acceptedAt;
  
  ContactInvitation({
    required this.id,
    required this.phoneNumber,
    required this.sentAt,
    this.isAccepted = false,
    this.acceptedAt,
  });
}
