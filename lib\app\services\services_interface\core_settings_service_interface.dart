import 'package:flutter/material.dart';
import 'app_services_interface.dart';

/// Interface for core application settings
/// Handles fundamental app settings like theme, notifications, privacy, and system preferences
abstract class CoreSettingsServiceAbstract extends InitializableServiceAbstract {
  // ========================================
  // THEME AND APPEARANCE SETTINGS
  // ========================================
  
  bool get isDarkMode;
  Future<void> setDarkMode(bool enabled);
  
  ThemeMode get themeMode;
  Future<void> setThemeMode(ThemeMode mode);
  
  String? get customThemeId;
  Future<void> setCustomThemeId(String? themeId);
  
  Color? get accentColor;
  Future<void> setAccentColor(Color? color);
  
  double get fontSize;
  Future<void> setFontSize(double size);
  
  bool get useSystemTheme;
  Future<void> setUseSystemTheme(bool enabled);
  
  // ========================================
  // GENERAL NOTIFICATION SETTINGS
  // ========================================
  
  bool get notificationsEnabled;
  Future<void> setNotificationsEnabled(bool enabled);
  
  bool get soundEnabled;
  Future<void> setSoundEnabled(bool enabled);
  
  bool get vibrationEnabled;
  Future<void> setVibrationEnabled(bool enabled);
  
  bool get showNotificationPreview;
  Future<void> setShowNotificationPreview(bool enabled);
  
  // ========================================
  // QUIET HOURS SETTINGS
  // ========================================
  
  TimeOfDay? get quietHoursStart;
  Future<void> setQuietHoursStart(TimeOfDay? time);
  
  TimeOfDay? get quietHoursEnd;
  Future<void> setQuietHoursEnd(TimeOfDay? time);
  
  bool get weekendQuietMode;
  Future<void> setWeekendQuietMode(bool enabled);
  
  // ========================================
  // PRIVACY SETTINGS
  // ========================================
  
  bool get showLastSeen;
  Future<void> setShowLastSeen(bool enabled);
  
  bool get showProfilePhoto;
  Future<void> setShowProfilePhoto(bool enabled);
  
  bool get allowContactSync;
  Future<void> setAllowContactSync(bool enabled);
  
  PrivacyLevel get profileVisibility;
  Future<void> setProfileVisibility(PrivacyLevel level);
  
  PrivacyLevel get messagingPermission;
  Future<void> setMessagingPermission(PrivacyLevel level);
  
  // ========================================
  // SECURITY SETTINGS
  // ========================================
  
  bool get enableTwoFactorAuth;
  Future<void> setEnableTwoFactorAuth(bool enabled);
  
  bool get enableBiometricLogin;
  Future<void> setEnableBiometricLogin(bool enabled);
  
  bool get autoLockEnabled;
  Future<void> setAutoLockEnabled(bool enabled);
  
  Duration get autoLockTimeout;
  Future<void> setAutoLockTimeout(Duration timeout);
  
  // ========================================
  // DATA & ANALYTICS SETTINGS
  // ========================================
  
  bool get analyticsEnabled;
  Future<void> setAnalyticsEnabled(bool enabled);
  
  bool get crashReportingEnabled;
  Future<void> setCrashReportingEnabled(bool enabled);
  
  bool get shareUsageData;
  Future<void> setShareUsageData(bool enabled);
  
  bool get personalizedAds;
  Future<void> setPersonalizedAds(bool enabled);
  
  // ========================================
  // SYSTEM & PERFORMANCE SETTINGS
  // ========================================
  
  bool get backgroundWorkEnabled;
  Future<void> setBackgroundWorkEnabled(bool enabled);
  
  bool get backgroundSyncEnabled;
  Future<void> setBackgroundSyncEnabled(bool enabled);
  
  SyncFrequency get syncFrequency;
  Future<void> setSyncFrequency(SyncFrequency frequency);
  
  bool get autoSaveEnabled;
  Future<void> setAutoSaveEnabled(bool enabled);
  
  Duration get autoSaveInterval;
  Future<void> setAutoSaveInterval(Duration interval);
  
  // ========================================
  // STORAGE & CACHE SETTINGS
  // ========================================
  
  int get maxCacheSizeMB;
  Future<void> setMaxCacheSizeMB(int sizeMB);
  
  DataUsagePreference get dataUsagePreference;
  Future<void> setDataUsagePreference(DataUsagePreference preference);
  
  bool get wifiOnlySync;
  Future<void> setWifiOnlySync(bool enabled);
  
  bool get roamingDataEnabled;
  Future<void> setRoamingDataEnabled(bool enabled);
  
  // ========================================
  // ACCESSIBILITY SETTINGS
  // ========================================
  
  bool get highContrastMode;
  Future<void> setHighContrastMode(bool enabled);
  
  bool get largeTextMode;
  Future<void> setLargeTextMode(bool enabled);
  
  bool get screenReaderEnabled;
  Future<void> setScreenReaderEnabled(bool enabled);
  
  bool get reduceMotion;
  Future<void> setReduceMotion(bool enabled);
  
  bool get hapticFeedbackEnabled;
  Future<void> setHapticFeedbackEnabled(bool enabled);
  
  // ========================================
  // DEVICE SETTINGS
  // ========================================
  
  String? get deviceName;
  Future<void> setDeviceName(String? name);
  
  String? get passCodeLock;
  Future<void> setPassCodeLock(String? code);
  
  // ========================================
  // SETTINGS MANAGEMENT
  // ========================================
  
  /// Stream of setting changes
  Stream<CoreSettingsChange> get settingsChanges;
  
  /// Reset all settings to defaults
  Future<void> resetToDefaults();
  
  /// Export all settings
  Future<Map<String, dynamic>> exportSettings();
  
  /// Import settings from map
  Future<bool> importSettings(Map<String, dynamic> settings);
  
  /// Validate all current settings
  Future<SettingsValidationResult> validateSettings();
  
  /// Get settings metadata
  SettingsMetadata get metadata;
}

/// Core settings change event
class CoreSettingsChange {
  final String settingKey;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;
  
  CoreSettingsChange({
    required this.settingKey,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
  });
}

/// Privacy levels for various settings
enum PrivacyLevel {
  everyone,
  contacts,
  nobody,
}

/// Sync frequency options
enum SyncFrequency {
  realtime,
  hourly,
  daily,
  manual,
}

/// Data usage preferences
enum DataUsagePreference {
  low,
  medium,
  high,
}

/// Settings validation result
class SettingsValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  
  SettingsValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });
}

/// Settings metadata
class SettingsMetadata {
  final DateTime lastModified;
  final DateTime createdAt;
  final int version;
  final String? deviceId;
  
  SettingsMetadata({
    required this.lastModified,
    required this.createdAt,
    required this.version,
    this.deviceId,
  });
}
