import 'app_services_interface.dart';

/// Interface for encryption service - handles cryptographic operations
abstract class EncryptionServiceAbstract extends InitializableServiceAbstract {
  Future<KeyPair> generateKeyPair();
  Future<String> encrypt(String plainText, String publicKey);
  Future<String> decrypt(String cipherText, String privateKey);
  Future<String> encryptMessage(String message, String roomId);
  Future<String> decryptMessage(String encryptedMessage, String roomId);
  Future<String> hashPassword(String password);
  Future<bool> verifyPassword(String password, String hash);
  Future<String> generateSecureToken();
  Future<String> generateRoomKey();
  Future<void> rotateRoomKey(String roomId);
  Future<void> exportKeys(String password);
  Future<void> importKeys(String keyData, String password);
  void dispose();
}

// Supporting classes
enum EncryptionAlgorithm { aes256, rsa2048, rsa4096, ecdsa }

enum HashAlgorithm { sha256, sha512, bcrypt, argon2 }

class KeyPair {
  final String publicKey;
  final String privateKey;
  final EncryptionAlgorithm algorithm;
  final DateTime createdAt;
  final DateTime? expiresAt;
  
  KeyPair({
    required this.publicKey,
    required this.privateKey,
    required this.algorithm,
    required this.createdAt,
    this.expiresAt,
  });
}

class EncryptedData {
  final String data;
  final String algorithm;
  final String? iv;
  final String? salt;
  final DateTime timestamp;
  
  EncryptedData({
    required this.data,
    required this.algorithm,
    this.iv,
    this.salt,
    required this.timestamp,
  });
}
