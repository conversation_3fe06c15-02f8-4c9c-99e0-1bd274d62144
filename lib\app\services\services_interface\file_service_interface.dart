import 'app_services_interface.dart';

/// Interface for file service - handles file operations
abstract class FileServiceAbstract extends InitializableServiceAbstract {
  Future<String?> pickFile(FileType type);
  Future<List<String>> pickMultipleFiles(FileType type);
  Future<String?> captureImage();
  Future<String?> captureVideo();
  Future<String?> recordAudio(Duration maxDuration);
  Future<String?> compressImage(String imagePath, {int quality = 80});
  Future<String?> compressVideo(String videoPath);
  Future<String?> generateThumbnail(String videoPath);
  Future<String> uploadFile(String filePath, FileType type);
  Future<String?> downloadFile(String url, String fileName);
  Future<bool> deleteFile(String filePath);
  Future<int> getFileSize(String filePath);
  Future<String> getFileHash(String filePath);
  void dispose();
}

// Supporting classes
enum FileType { image, video, audio, document, any }

enum CompressionQuality { low, medium, high, lossless }

class FileInfo {
  final String path;
  final String name;
  final String extension;
  final int size;
  final FileType type;
  final DateTime createdAt;
  final DateTime modifiedAt;
  final String? mimeType;
  
  FileInfo({
    required this.path,
    required this.name,
    required this.extension,
    required this.size,
    required this.type,
    required this.createdAt,
    required this.modifiedAt,
    this.mimeType,
  });
}

class UploadProgress {
  final double progress;
  final int bytesUploaded;
  final int totalBytes;
  final String? error;
  
  UploadProgress({
    required this.progress,
    required this.bytesUploaded,
    required this.totalBytes,
    this.error,
  });
}
