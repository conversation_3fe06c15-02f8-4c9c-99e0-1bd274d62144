import 'app_services_interface.dart';
import '../../data/models/identity_settings_model.dart';

/// Interface for identity-specific settings management
/// Handles settings that can vary per user identity (personal, business, incognito, etc.)
abstract class IdentitySettingsServiceAbstract extends InitializableServiceAbstract {
  // ========================================
  // CORE IDENTITY SETTINGS OPERATIONS
  // ========================================

  /// Get settings for a specific identity
  Future<IdentitySettings> getSettings(String myIdentityCardId);

  /// Update settings for a specific identity
  Future<void> updateSettings(IdentitySettings settings);

  /// Stream of identity settings changes for a specific identity
  Stream<IdentitySettings> getSettingsStream(String myIdentityCardId);

  /// Get settings for all identities of the current user
  Future<List<IdentitySettings>> getAllSettings();

  /// Create default settings for a new identity
  Future<IdentitySettings> createSettings(String myIdentityCardId, {bool isBusinessIdentity = false});

  /// Delete settings for an identity (when identity is deleted)
  Future<void> deleteSettings(String myIdentityCardId);

  /// Reset identity settings to defaults
  Future<void> resetSettings(String myIdentityCardId);

  // ========================================
  // IDENTITY DISPLAY SETTINGS
  // ========================================

  Future<String?> getDisplayName(String myIdentityCardId);
  Future<void> setDisplayName(String myIdentityCardId, String? displayName);

  Future<String?> getStatusMessage(String myIdentityCardId);
  Future<void> setStatusMessage(String myIdentityCardId, String? statusMessage);

  Future<String?> getAvatarUrl(String myIdentityCardId);
  Future<void> setAvatarUrl(String myIdentityCardId, String? avatarUrl);

  Future<String?> getCustomTheme(String myIdentityCardId);
  Future<void> setCustomTheme(String myIdentityCardId, String? themeId);

  // ========================================
  // PRIVACY SETTINGS PER IDENTITY
  // ========================================

  Future<String> getWhoCanSeeProfile(String myIdentityCardId);
  Future<void> setWhoCanSeeProfile(String myIdentityCardId, String level);

  Future<String> getWhoCanMessageMe(String myIdentityCardId);
  Future<void> setWhoCanMessageMe(String myIdentityCardId, String level);

  Future<bool> getShowLastSeen(String myIdentityCardId);
  Future<void> setShowLastSeen(String myIdentityCardId, bool enabled);

  Future<bool> getShowOnlineStatus(String myIdentityCardId);
  Future<void> setShowOnlineStatus(String myIdentityCardId, bool enabled);

  Future<bool> getShowProfilePhoto(String myIdentityCardId);
  Future<void> setShowProfilePhoto(String myIdentityCardId, bool enabled);

  Future<bool> getSendReadReceipts(String myIdentityCardId);
  Future<void> setSendReadReceipts(String myIdentityCardId, bool enabled);

  Future<bool> getShowTypingIndicators(String myIdentityCardId);
  Future<void> setShowTypingIndicators(String myIdentityCardId, bool enabled);

  Future<bool> getAllowForwarding(String myIdentityCardId);
  Future<void> setAllowForwarding(String myIdentityCardId, bool enabled);

  Future<bool> getAllowScreenshots(String myIdentityCardId);
  Future<void> setAllowScreenshots(String myIdentityCardId, bool enabled);

  // ========================================
  // MESSAGING PREFERENCES PER IDENTITY
  // ========================================

  Future<bool> getAutoDownloadMedia(String myIdentityCardId);
  Future<void> setAutoDownloadMedia(String myIdentityCardId, bool enabled);

  Future<String> getMediaDownloadPreference(String myIdentityCardId);
  Future<void> setMediaDownloadPreference(String myIdentityCardId, String preference);

  Future<bool> getCompressImages(String myIdentityCardId);
  Future<void> setCompressImages(String myIdentityCardId, bool enabled);

  Future<bool> getEnableVoiceMessages(String myIdentityCardId);
  Future<void> setEnableVoiceMessages(String myIdentityCardId, bool enabled);

  Future<String> getVoiceMessageQuality(String myIdentityCardId);
  Future<void> setVoiceMessageQuality(String myIdentityCardId, String quality);

  Future<String?> getChatWallpaper(String myIdentityCardId);
  Future<void> setChatWallpaper(String myIdentityCardId, String? wallpaper);

  Future<double> getChatFontSize(String myIdentityCardId);
  Future<void> setChatFontSize(String myIdentityCardId, double size);

  Future<bool> getShowTimestamps(String myIdentityCardId);
  Future<void> setShowTimestamps(String myIdentityCardId, bool enabled);

  Future<bool> getEnterToSend(String myIdentityCardId);
  Future<void> setEnterToSend(String myIdentityCardId, bool enabled);

  // ========================================
  // NOTIFICATION SETTINGS PER IDENTITY
  // ========================================

  Future<bool> getMessageNotificationsEnabled(String myIdentityCardId);
  Future<void> setMessageNotificationsEnabled(String myIdentityCardId, bool enabled);

  Future<bool> getGroupMessageNotificationsEnabled(String myIdentityCardId);
  Future<void> setGroupMessageNotificationsEnabled(String myIdentityCardId, bool enabled);

  Future<bool> getMentionNotificationsEnabled(String myIdentityCardId);
  Future<void> setMentionNotificationsEnabled(String myIdentityCardId, bool enabled);

  Future<String?> getMessageNotificationSound(String myIdentityCardId);
  Future<void> setMessageNotificationSound(String myIdentityCardId, String? sound);

  Future<bool> getShowNotificationPreview(String myIdentityCardId);
  Future<void> setShowNotificationPreview(String myIdentityCardId, bool enabled);

  Future<bool> getUseGlobalQuietHours(String myIdentityCardId);
  Future<void> setUseGlobalQuietHours(String myIdentityCardId, bool enabled);

  // ========================================
  // BUSINESS SETTINGS PER IDENTITY
  // ========================================

  Future<bool> getIsBusinessIdentity(String myIdentityCardId);
  Future<void> setIsBusinessIdentity(String myIdentityCardId, bool enabled);

  Future<String?> getBusinessCategory(String myIdentityCardId);
  Future<void> setBusinessCategory(String myIdentityCardId, String? category);

  Future<bool> getShowBusinessHours(String myIdentityCardId);
  Future<void> setShowBusinessHours(String myIdentityCardId, bool enabled);

  Future<bool> getEnableAutoReply(String myIdentityCardId);
  Future<void> setEnableAutoReply(String myIdentityCardId, bool enabled);

  Future<String?> getAutoReplyMessage(String myIdentityCardId);
  Future<void> setAutoReplyMessage(String myIdentityCardId, String? message);

  // ========================================
  // E-COMMERCE SETTINGS PER IDENTITY
  // ========================================

  Future<bool> getEnableOrderNotifications(String myIdentityCardId);
  Future<void> setEnableOrderNotifications(String myIdentityCardId, bool enabled);

  Future<String?> getPreferredCurrency(String myIdentityCardId);
  Future<void> setPreferredCurrency(String myIdentityCardId, String? currency);

  Future<String?> getDefaultShippingAddress(String myIdentityCardId);
  Future<void> setDefaultShippingAddress(String myIdentityCardId, String? address);

  // ========================================
  // SECURITY SETTINGS PER IDENTITY
  // ========================================

  Future<bool> getEnableMessageEncryption(String myIdentityCardId);
  Future<void> setEnableMessageEncryption(String myIdentityCardId, bool enabled);

  Future<bool> getEnableDisappearingMessages(String myIdentityCardId);
  Future<void> setEnableDisappearingMessages(String myIdentityCardId, bool enabled);

  Future<int> getDefaultDisappearingMessageTimer(String myIdentityCardId);
  Future<void> setDefaultDisappearingMessageTimer(String myIdentityCardId, int seconds);

  Future<bool> getEnableMultiDeviceSync(String myIdentityCardId);
  Future<void> setEnableMultiDeviceSync(String myIdentityCardId, bool enabled);

  // ========================================
  // SETTINGS MANAGEMENT
  // ========================================

  /// Export identity settings
  Future<String> exportSettings(String myIdentityCardId);

  /// Import identity settings
  Future<bool> importSettings(String myIdentityCardId, String settingsData);

  /// Validate identity settings data
  Future<bool> validateSettings(String settingsData);

  /// Get identity settings metadata
  Future<IdentitySettingsMetadata> getMetadata(String myIdentityCardId);

  /// Stream of setting changes for a specific identity
  Stream<IdentitySettingsChangeEvent> getSettingsChanges(String myIdentityCardId);

  /// Add listener for specific identity setting changes
  void addSettingListener(String myIdentityCardId, String key, Function(dynamic value) listener);

  /// Remove identity setting listener
  void removeSettingListener(String myIdentityCardId, String key, Function(dynamic value) listener);
}

// ========================================
// SUPPORTING CLASSES
// ========================================

/// Identity settings change event
class IdentitySettingsChangeEvent {
  final String myIdentityCardId;
  final String settingKey;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;

  IdentitySettingsChangeEvent({
    required this.myIdentityCardId,
    required this.settingKey,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
  });
}

/// Identity settings metadata
class IdentitySettingsMetadata {
  final String myIdentityCardId;
  final DateTime lastModified;
  final DateTime createdAt;
  final int version;
  final bool isBusinessIdentity;

  IdentitySettingsMetadata({
    required this.myIdentityCardId,
    required this.lastModified,
    required this.createdAt,
    required this.version,
    required this.isBusinessIdentity,
  });
}

/// Privacy levels for identity settings
enum PrivacyLevel {
  everyone,
  contacts,
  nobody,
}

/// Media download preferences
enum MediaDownloadPreference {
  wifiOnly,
  always,
  never,
}

/// Voice message quality levels
enum VoiceMessageQuality {
  low,
  medium,
  high,
}
