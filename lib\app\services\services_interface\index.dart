/// Service Interfaces Export File
///
/// This file provides a single import point for all service interfaces
/// in the application. It promotes clean architecture by separating
/// interface definitions from implementations.
///
/// The interfaces are organized into logical groups for better maintainability.

// ========================================
// CORE SERVICE INTERFACES
// ========================================

/// Base interface for all services
export 'app_services_interface.dart';

// ========================================
// SPECIFIC SERVICE INTERFACES
// ========================================

/// Database service interface
export 'objectbox_service_interface.dart';

/// Settings management interface
export 'settings_service_interface.dart';

/// Localization and internationalization interface
export 'localization_service_interface.dart';

// ========================================
// SERVICE MANAGEMENT INTERFACES
// ========================================

/// Service initialization and lifecycle management
export 'service_initializer_interface.dart';

/// Service registry for instance management
export 'service_registry_interface.dart';

/// Service health monitoring
export 'service_health_monitor_interface.dart';

// ========================================
// MESSAGING AND COMMUNICATION SERVICES
// ========================================

/// WebSocket service for real-time communication
export 'websocket_service_interface.dart';

/// Authentication and authorization service
export 'authentication_service_interface.dart';

/// Message handling service
export 'message_service_interface.dart';

/// Room management service
export 'room_service_interface.dart';

/// Data synchronization service
export 'sync_service_interface.dart';

/// Offline queue management service
export 'offline_queue_service_interface.dart';

// ========================================
// SYSTEM AND UTILITY SERVICES
// ========================================

/// Network connectivity monitoring service
export 'network_service_interface.dart';

/// File operations service
export 'file_service_interface.dart';

/// Encryption and security service
export 'encryption_service_interface.dart';

/// Media processing service
export 'media_service_interface.dart';

/// Data caching service
export 'cache_service_interface.dart';

/// Push and local notifications service
export 'notification_service_interface.dart';

// ========================================
// USER AND CONTENT SERVICES
// ========================================

/// Contact management service
export 'contact_service_interface.dart';

/// User presence and activity service
export 'presence_service_interface.dart';

/// Location and geolocation service
export 'location_service_interface.dart';

/// Voice recording and audio playback service
export 'voice_service_interface.dart';

/// Text translation service
export 'translation_service_interface.dart';

/// App theming service
export 'theme_service_interface.dart';

// ========================================
// DATA AND ANALYTICS SERVICES
// ========================================

/// Backup and restore service
export 'backup_service_interface.dart';

/// Analytics and tracking service
export 'analytics_service_interface.dart';

/// App permissions service
export 'permission_service_interface.dart';

/// Search functionality service
export 'search_service_interface.dart';
