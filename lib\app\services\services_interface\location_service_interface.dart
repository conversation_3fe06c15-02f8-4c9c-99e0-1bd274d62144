import 'app_services_interface.dart';

/// Interface for location service - handles location and geolocation operations
abstract class LocationServiceAbstract extends InitializableServiceAbstract {
  Future<LocationData?> getCurrentLocation();
  Stream<LocationData> get locationStream;
  Future<void> startLocationTracking();
  Future<void> stopLocationTracking();
  Future<String?> getAddressFromCoordinates(double latitude, double longitude);
  Future<LocationData?> getLocationFromAddress(String address);
  Future<List<PlaceResult>> searchNearbyPlaces(double latitude, double longitude, String query);
  Future<bool> requestLocationPermission();
  void dispose();
}

// Supporting classes
enum LocationAccuracy { low, medium, high, best }

class LocationData {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final double? altitude;
  final double? speed;
  final double? heading;
  final DateTime timestamp;
  
  LocationData({
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.altitude,
    this.speed,
    this.heading,
    required this.timestamp,
  });
}

class PlaceResult {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String? category;
  final double? rating;
  final String? phoneNumber;
  final String? website;
  final Map<String, dynamic>? metadata;
  
  PlaceResult({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.category,
    this.rating,
    this.phoneNumber,
    this.website,
    this.metadata,
  });
}

class GeofenceRegion {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final double radius;
  final bool isActive;
  
  GeofenceRegion({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.radius,
    this.isActive = true,
  });
}
