import 'app_services_interface.dart';
import 'file_service_interface.dart';

/// Interface for media service - handles media processing operations
abstract class MediaServiceAbstract extends InitializableServiceAbstract {
  Future<String?> processImage(
    String imagePath,
    ImageProcessingOptions options,
  );
  Future<String?> processVideo(
    String videoPath,
    VideoProcessingOptions options,
  );
  Future<String?> processAudio(
    String audioPath,
    AudioProcessingOptions options,
  );
  Future<String?> generateImageThumbnail(String imagePath, Size thumbnailSize);
  Future<String?> generateVideoThumbnail(String videoPath, Size thumbnailSize);
  Future<Duration> getVideoDuration(String videoPath);
  Future<Duration> getAudioDuration(String audioPath);
  Future<String?> extractAudioFromVideo(String videoPath);
  Future<bool> validateMediaFile(String filePath, FileType type);
  void dispose();
}

// Supporting classes
// Note: FileType enum is imported from file_service_interface.dart

enum ImageFormat { jpeg, png, webp, gif }

enum VideoFormat { mp4, avi, mov, mkv }

enum AudioFormat { mp3, wav, aac, flac }

class Size {
  final int width;
  final int height;
  Size({required this.width, required this.height});
}

class ImageProcessingOptions {
  final int? quality;
  final Size? maxSize;
  final ImageFormat? format;
  final bool? removeMetadata;
  final double? rotation;

  ImageProcessingOptions({
    this.quality,
    this.maxSize,
    this.format,
    this.removeMetadata,
    this.rotation,
  });
}

class VideoProcessingOptions {
  final int? quality;
  final Size? maxSize;
  final Duration? maxDuration;
  final VideoFormat? format;
  final int? bitrate;
  final int? frameRate;

  VideoProcessingOptions({
    this.quality,
    this.maxSize,
    this.maxDuration,
    this.format,
    this.bitrate,
    this.frameRate,
  });
}

class AudioProcessingOptions {
  final int? bitRate;
  final int? sampleRate;
  final AudioFormat? format;
  final bool? removeNoise;
  final double? volume;

  AudioProcessingOptions({
    this.bitRate,
    this.sampleRate,
    this.format,
    this.removeNoise,
    this.volume,
  });
}
