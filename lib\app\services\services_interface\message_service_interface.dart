import 'app_services_interface.dart';

/// Interface for message service - handles message operations
abstract class MessageServiceAbstract extends InitializableServiceAbstract {
  Future<Message> sendTextMessage(String roomId, String content);
  Future<Message> sendMediaMessage(String roomId, String filePath, MessageType type);
  Future<Message> sendLocationMessage(String roomId, double latitude, double longitude);
  Future<Message> sendContactMessage(String roomId, String contactId);
  Future<Message> editMessage(String messageId, String newContent);
  Future<bool> deleteMessage(String messageId, bool deleteForEveryone);
  Future<bool> forwardMessage(String messageId, List<String> roomIds);
  Future<bool> starMessage(String messageId);
  Future<bool> unstarMessage(String messageId);
  Future<void> markMessageAsRead(String messageId);
  Future<void> markRoomAsRead(String roomId);
  Stream<List<Message>> getMessagesStream(String roomId);
  Future<List<Message>> searchMessages(String query, {String? roomId});
  Future<void> retryFailedMessages();
  void dispose();
}

// Supporting classes
enum MessageType { text, image, video, audio, document, location, contact }

enum MessageStatus { sending, sent, delivered, read, failed }

class Message {
  final String id;
  final String roomId;
  final String senderId;
  final String content;
  final MessageType type;
  final MessageStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isStarred;
  final String? replyToMessageId;
  final Map<String, dynamic>? metadata;
  
  Message({
    required this.id,
    required this.roomId,
    required this.senderId,
    required this.content,
    required this.type,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.isStarred = false,
    this.replyToMessageId,
    this.metadata,
  });
}
