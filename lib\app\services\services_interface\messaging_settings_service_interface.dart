import 'app_services_interface.dart';

/// Interface for messaging-specific settings
abstract class MessagingSettingsServiceAbstract extends InitializableServiceAbstract {
  // ========================================
  // MESSAGE BEHAVIOR SETTINGS
  // ========================================
  
  /// Read receipts and typing indicators
  bool get sendReadReceipts;
  Future<void> setSendReadReceipts(bool enabled);
  
  bool get showTypingIndicators;
  Future<void> setShowTypingIndicators(bool enabled);
  
  bool get showOnlineStatus;
  Future<void> setShowOnlineStatus(bool enabled);
  
  // ========================================
  // MESSAGE ENCRYPTION & SECURITY
  // ========================================
  
  bool get enableMessageEncryption;
  Future<void> setEnableMessageEncryption(bool enabled);
  
  bool get enableScreenshotBlocking;
  Future<void> setEnableScreenshotBlocking(bool enabled);
  
  // ========================================
  // MEDIA & FILE SETTINGS
  // ========================================
  
  bool get autoDownloadMedia;
  Future<void> setAutoDownloadMedia(bool enabled);
  
  bool get compressImages;
  Future<void> setCompressImages(bool enabled);
  
  String get mediaDownloadPreference; // 'wifi_only', 'always', 'never'
  Future<void> setMediaDownloadPreference(String preference);
  
  // ========================================
  // CHAT INTERFACE SETTINGS
  // ========================================
  
  bool get enterToSend;
  Future<void> setEnterToSend(bool enabled);
  
  bool get showTimestamps;
  Future<void> setShowTimestamps(bool enabled);
  
  String? get chatWallpaper;
  Future<void> setChatWallpaper(String? wallpaper);
  
  double get chatFontSize;
  Future<void> setChatFontSize(double size);
  
  // ========================================
  // VOICE & VIDEO SETTINGS
  // ========================================
  
  bool get enableVoiceMessages;
  Future<void> setEnableVoiceMessages(bool enabled);
  
  bool get enableVideoMessages;
  Future<void> setEnableVideoMessages(bool enabled);
  
  String get voiceMessageQuality; // 'low', 'medium', 'high'
  Future<void> setVoiceMessageQuality(String quality);
  
  bool get autoPlayVoiceMessages;
  Future<void> setAutoPlayVoiceMessages(bool enabled);
  
  // ========================================
  // NOTIFICATION SETTINGS
  // ========================================
  
  bool get messageNotificationsEnabled;
  Future<void> setMessageNotificationsEnabled(bool enabled);
  
  bool get groupMessageNotificationsEnabled;
  Future<void> setGroupMessageNotificationsEnabled(bool enabled);
  
  bool get mentionNotificationsEnabled;
  Future<void> setMentionNotificationsEnabled(bool enabled);
  
  String? get messageNotificationSound;
  Future<void> setMessageNotificationSound(String? sound);
  
  // ========================================
  // DATA RETENTION SETTINGS
  // ========================================
  
  bool get autoDeleteOldMessages;
  Future<void> setAutoDeleteOldMessages(bool enabled);
  
  int get messageRetentionDays;
  Future<void> setMessageRetentionDays(int days);
  
  // ========================================
  // SETTINGS CHANGE NOTIFICATIONS
  // ========================================
  
  Stream<MessagingSettingsChange> get settingsChanges;
  
  /// Reset all messaging settings to defaults
  Future<void> resetToDefaults();
  
  /// Export messaging settings
  Future<Map<String, dynamic>> exportSettings();
  
  /// Import messaging settings
  Future<bool> importSettings(Map<String, dynamic> settings);
}

/// Messaging settings change event
class MessagingSettingsChange {
  final String settingKey;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;
  
  MessagingSettingsChange({
    required this.settingKey,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
  });
}

/// Media download preferences
enum MediaDownloadPreference {
  wifiOnly,
  always,
  never,
}

/// Voice message quality levels
enum VoiceMessageQuality {
  low,
  medium,
  high,
}

/// Message notification types
enum MessageNotificationType {
  all,
  mentionsOnly,
  none,
}
