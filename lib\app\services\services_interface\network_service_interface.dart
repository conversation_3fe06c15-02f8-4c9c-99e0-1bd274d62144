import 'app_services_interface.dart';

/// Interface for network service - handles network connectivity monitoring
abstract class NetworkServiceAbstract extends InitializableServiceAbstract {
  Stream<NetworkStatus> get networkStatusStream;
  Future<bool> isConnected();
  Future<bool> hasInternetAccess();
  NetworkStatus get currentStatus;
  Future<void> startMonitoring();
  Future<void> stopMonitoring();
  Future<NetworkQuality> testNetworkQuality();
  Future<void> handleNetworkChange(NetworkStatus status);
  void dispose();
}

// Supporting classes
enum NetworkStatus { connected, disconnected, connecting, unknown }

enum NetworkType { wifi, mobile, ethernet, other }

enum NetworkQuality { poor, fair, good, excellent }

class NetworkInfo {
  final NetworkStatus status;
  final NetworkType type;
  final NetworkQuality quality;
  final String? ssid;
  final int? signalStrength;
  final double? speed;
  final DateTime timestamp;
  
  NetworkInfo({
    required this.status,
    required this.type,
    required this.quality,
    this.ssid,
    this.signalStrength,
    this.speed,
    required this.timestamp,
  });
}
