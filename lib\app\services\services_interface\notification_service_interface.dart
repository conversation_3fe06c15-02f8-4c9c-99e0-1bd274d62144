import 'app_services_interface.dart';

/// Interface for notification service - handles push notifications and local notifications
abstract class NotificationServiceAbstract extends InitializableServiceAbstract {
  Future<bool> requestPermissions();
  Future<String?> getDeviceToken();
  Future<void> showLocalNotification(LocalNotification notification);
  Future<void> scheduleNotification(LocalNotification notification, DateTime scheduledTime);
  Future<void> cancelNotification(String notificationId);
  Future<void> cancelAllNotifications();
  Future<void> setBadgeCount(int count);
  Future<void> clearBadge();
  Stream<NotificationResponse> get notificationResponseStream;
  Future<void> handleForegroundNotification(RemoteMessage message);
  Future<void> handleBackgroundNotification(RemoteMessage message);
  Future<void> updateNotificationSettings(NotificationSettings settings);
  void dispose();
}

// Supporting classes
class LocalNotification {
  final String id;
  final String title;
  final String body;
  final String? icon;
  final String? sound;
  final Map<String, dynamic>? data;
  final NotificationPriority priority;
  
  LocalNotification({
    required this.id,
    required this.title,
    required this.body,
    this.icon,
    this.sound,
    this.data,
    this.priority = NotificationPriority.normal,
  });
}

class NotificationResponse {
  final String notificationId;
  final String? actionId;
  final Map<String, dynamic>? data;
  NotificationResponse({required this.notificationId, this.actionId, this.data});
}

class RemoteMessage {
  final String messageId;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  RemoteMessage({required this.messageId, required this.title, required this.body, required this.data});
}

class NotificationSettings {
  final bool enabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool showPreview;
  NotificationSettings({
    required this.enabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.showPreview,
  });
}

enum NotificationPriority { low, normal, high, urgent }
