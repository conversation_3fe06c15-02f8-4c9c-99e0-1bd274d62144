import 'app_services_interface.dart';
import 'message_service_interface.dart';

/// Interface for offline queue service - handles offline operations
abstract class OfflineQueueServiceAbstract
    extends InitializableServiceAbstract {
  Future<void> enqueueMessage(Message message);
  Future<void> enqueueRoomAction(RoomAction action);
  Future<void> enqueueUserAction(UserAction action);
  Future<void> processQueue();
  Future<void> retryFailedItems();
  Future<void> clearQueue();
  Stream<QueueStatus> get queueStatusStream;
  Future<List<QueueItem>> getPendingItems();
  Future<void> removeQueueItem(String itemId);
  Future<void> pauseQueue();
  Future<void> resumeQueue();
  void dispose();
}

// Supporting classes
enum QueueStatus { idle, processing, paused, error }

class QueueItem {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final int retryCount;
  final DateTime? lastRetryAt;
  final String? error;

  QueueItem({
    required this.id,
    required this.type,
    required this.data,
    required this.createdAt,
    this.retryCount = 0,
    this.lastRetryAt,
    this.error,
  });
}

class RoomAction {
  final String type;
  final String roomId;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  RoomAction({
    required this.type,
    required this.roomId,
    required this.data,
    required this.timestamp,
  });
}

class UserAction {
  final String type;
  final String userId;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  UserAction({
    required this.type,
    required this.userId,
    required this.data,
    required this.timestamp,
  });
}

// Note: Message class is imported from message_service_interface.dart
