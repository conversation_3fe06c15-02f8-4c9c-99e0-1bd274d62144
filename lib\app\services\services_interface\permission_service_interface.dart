import 'app_services_interface.dart';

/// Interface for permission service - handles app permissions
abstract class PermissionServiceAbstract extends InitializableServiceAbstract {
  Future<PermissionResult> requestPermission(Permission permission);
  Future<Map<Permission, PermissionResult>> requestMultiplePermissions(List<Permission> permissions);
  Future<PermissionResult> checkPermission(Permission permission);
  Future<bool> shouldShowRationale(Permission permission);
  Future<void> openAppSettings();
  Stream<PermissionResult> watchPermission(Permission permission);
  void dispose();
}

// Supporting classes
enum Permission {
  camera,
  microphone,
  location,
  storage,
  contacts,
  phone,
  sms,
  calendar,
  photos,
  notification,
  bluetooth,
  biometric,
}

enum PermissionResult {
  granted,
  denied,
  permanentlyDenied,
  restricted,
  limited,
  unknown,
}

class PermissionStatus {
  final Permission permission;
  final PermissionResult result;
  final DateTime checkedAt;
  final bool shouldShowRationale;
  
  PermissionStatus({
    required this.permission,
    required this.result,
    required this.checkedAt,
    this.shouldShowRationale = false,
  });
}

class PermissionRequest {
  final List<Permission> permissions;
  final String? rationale;
  final bool openSettingsIfDenied;
  
  PermissionRequest({
    required this.permissions,
    this.rationale,
    this.openSettingsIfDenied = false,
  });
}
