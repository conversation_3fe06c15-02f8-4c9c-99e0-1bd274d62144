import 'app_services_interface.dart';

/// Interface for presence service - handles user presence and activity status
abstract class PresenceServiceAbstract extends InitializableServiceAbstract {
  Future<void> updatePresence(PresenceStatus status);
  Future<void> startPresenceUpdates();
  Future<void> stopPresenceUpdates();
  Stream<Map<String, PresenceInfo>> get presenceStream;
  Future<PresenceInfo?> getPresence(String identityId);
  Future<void> setTypingIndicator(String roomId, bool isTyping);
  Stream<TypingIndicator> get typingIndicatorStream;
  void dispose();
}

// Supporting classes
enum PresenceStatus { online, away, busy, offline, invisible }

class PresenceInfo {
  final PresenceStatus status;
  final DateTime lastSeen;
  final String? customMessage;
  final bool isTyping;
  final String? currentActivity;
  
  PresenceInfo({
    required this.status,
    required this.lastSeen,
    this.customMessage,
    this.isTyping = false,
    this.currentActivity,
  });
}

class TypingIndicator {
  final String roomId;
  final String userId;
  final bool isTyping;
  final DateTime timestamp;
  
  TypingIndicator({
    required this.roomId,
    required this.userId,
    required this.isTyping,
    required this.timestamp,
  });
}

class ActivityStatus {
  final String userId;
  final String activity;
  final DateTime startedAt;
  final Map<String, dynamic>? metadata;
  
  ActivityStatus({
    required this.userId,
    required this.activity,
    required this.startedAt,
    this.metadata,
  });
}
