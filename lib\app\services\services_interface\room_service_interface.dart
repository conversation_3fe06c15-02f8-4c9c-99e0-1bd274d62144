import 'app_services_interface.dart';

/// Interface for room service - handles chat room operations
abstract class RoomServiceAbstract extends InitializableServiceAbstract {
  Future<Room> createPrivateRoom(String participantId);
  Future<Room> createGroupRoom(String title, List<String> participantIds);
  Future<Room> createChannelRoom(String title, String? description);
  Future<Room> createOrderRoom(String title, String category);
  Future<void> joinRoom(String roomId);
  Future<void> leaveRoom(String roomId);
  Future<void> addParticipant(String roomId, String participantId);
  Future<void> removeParticipant(String roomId, String participantId);
  Future<void> updateRoomInfo(String roomId, String? title, String? description);
  Future<void> archiveRoom(String roomId);
  Future<void> unarchiveRoom(String roomId);
  Future<void> muteRoom(String roomId, Duration? duration);
  Future<void> unmuteRoom(String roomId);
  Future<void> pinRoom(String roomId);
  Future<void> unpinRoom(String roomId);
  Stream<List<Room>> getRoomsStream();
  Future<List<RoomParticipant>> getRoomParticipants(String roomId);
  void dispose();
}

// Supporting classes
enum RoomType { private, group, channel, order }

enum RoomStatus { active, archived, deleted }

enum ParticipantRole { member, admin, owner }

class Room {
  final String id;
  final String title;
  final String? description;
  final RoomType type;
  final RoomStatus status;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime? lastMessageAt;
  final bool isMuted;
  final bool isPinned;
  final bool isArchived;
  final int unreadCount;
  final String? lastMessageId;
  final List<String> participantIds;
  
  Room({
    required this.id,
    required this.title,
    this.description,
    required this.type,
    required this.status,
    this.avatarUrl,
    required this.createdAt,
    this.lastMessageAt,
    this.isMuted = false,
    this.isPinned = false,
    this.isArchived = false,
    this.unreadCount = 0,
    this.lastMessageId,
    required this.participantIds,
  });
}

class RoomParticipant {
  final String id;
  final String roomId;
  final String userId;
  final ParticipantRole role;
  final DateTime joinedAt;
  final bool isActive;
  
  RoomParticipant({
    required this.id,
    required this.roomId,
    required this.userId,
    required this.role,
    required this.joinedAt,
    this.isActive = true,
  });
}
