import 'app_services_interface.dart';
import 'message_service_interface.dart';
import 'room_service_interface.dart';
import 'contact_service_interface.dart';

/// Interface for search service - handles search operations across the app
abstract class SearchServiceAbstract extends InitializableServiceAbstract {
  Future<SearchResults> searchGlobal(String query);
  Future<List<Message>> searchMessages(String query, {String? roomId});
  Future<List<Room>> searchRooms(String query);
  Future<List<Identity>> searchContacts(String query);
  Future<void> indexContent(String id, String content, SearchType type);
  Future<void> removeFromIndex(String id, SearchType type);
  Future<void> clearIndex(SearchType type);
  Future<List<String>> getSearchSuggestions(String partialQuery);
  void dispose();
}

// Supporting classes
enum SearchType { message, room, contact, file, all }

class SearchResults {
  final List<Message> messages;
  final List<Room> rooms;
  final List<Identity> contacts;
  final List<FileResult> files;
  final int totalCount;
  final String query;
  final DateTime searchedAt;

  SearchResults({
    required this.messages,
    required this.rooms,
    required this.contacts,
    required this.files,
    required this.totalCount,
    required this.query,
    required this.searchedAt,
  });
}

class FileResult {
  final String id;
  final String name;
  final String path;
  final String type;
  final int size;
  final DateTime modifiedAt;

  FileResult({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.size,
    required this.modifiedAt,
  });
}

class SearchFilter {
  final SearchType? type;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? roomId;
  final String? senderId;
  final List<String>? tags;

  SearchFilter({
    this.type,
    this.startDate,
    this.endDate,
    this.roomId,
    this.senderId,
    this.tags,
  });
}

// Note: Message, Room, and Identity classes are imported from their respective service interfaces
