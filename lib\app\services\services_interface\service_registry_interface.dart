import 'app_services_interface.dart';

/// Interface for service registry - manages service instances
///
/// This interface handles the registration and retrieval of service instances,
/// separate from initialization logic for better separation of concerns.
abstract class ServiceRegistryAbstract {
  // ========================================
  // SERVICE REGISTRATION
  // ========================================

  /// Register a service instance in the registry
  void register<T extends InitializableServiceAbstract>(T service);

  /// Unregister a service from the registry
  void unregister<T extends InitializableServiceAbstract>();

  /// Check if a service type is registered
  bool isRegistered<T extends InitializableServiceAbstract>();

  // ========================================
  // SERVICE RETRIEVAL
  // ========================================

  /// Get a service instance by type
  T get<T extends InitializableServiceAbstract>();

  /// Get all registered services
  List<InitializableServiceAbstract> getAllServices();

  /// Get services by their current state
  List<InitializableServiceAbstract> getServicesByState({
    bool? initialized,
    bool? started,
    bool? healthy,
  });

  // ========================================
  // REGISTRY MANAGEMENT
  // ========================================

  /// Clear all registered services
  void clear();

  /// Get count of registered services
  int get serviceCount;
}
