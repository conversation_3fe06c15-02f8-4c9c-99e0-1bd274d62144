import 'app_services_interface.dart';

/// Interface for settings migration and versioning
abstract class SettingsMigrationServiceAbstract extends InitializableServiceAbstract {
  /// Current settings schema version
  int get currentVersion;
  
  /// Get the version of stored settings
  Future<int> getStoredVersion();
  
  /// Check if migration is needed
  Future<bool> isMigrationNeeded();
  
  /// Perform migration from old version to current version
  Future<MigrationResult> migrate();
  
  /// Migrate specific version range
  Future<MigrationResult> migrateFromVersion(int fromVersion, int toVersion);
  
  /// Backup current settings before migration
  Future<String> backupSettings();
  
  /// Restore settings from backup
  Future<bool> restoreFromBackup(String backupData);
  
  /// Validate settings after migration
  Future<ValidationResult> validateMigratedSettings();
  
  /// Get available migration paths
  Future<List<MigrationPath>> getAvailableMigrations();
  
  /// Register a custom migration handler
  void registerMigrationHandler(int fromVersion, int toVersion, MigrationHandler handler);
  
  /// Stream of migration progress
  Stream<MigrationProgress> get migrationProgress;
}

/// Migration result
class MigrationResult {
  final bool success;
  final int fromVersion;
  final int toVersion;
  final List<String> appliedMigrations;
  final List<String> errors;
  final List<String> warnings;
  final Duration duration;
  final DateTime completedAt;
  
  MigrationResult({
    required this.success,
    required this.fromVersion,
    required this.toVersion,
    required this.appliedMigrations,
    this.errors = const [],
    this.warnings = const [],
    required this.duration,
    required this.completedAt,
  });
}

/// Migration validation result
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final Map<String, dynamic> validatedSettings;
  
  ValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
    this.validatedSettings = const {},
  });
}

/// Migration path information
class MigrationPath {
  final int fromVersion;
  final int toVersion;
  final String description;
  final bool isRequired;
  final List<String> changes;
  
  MigrationPath({
    required this.fromVersion,
    required this.toVersion,
    required this.description,
    this.isRequired = true,
    this.changes = const [],
  });
}

/// Migration progress information
class MigrationProgress {
  final int currentStep;
  final int totalSteps;
  final String currentOperation;
  final double progress; // 0.0 to 1.0
  final String? error;
  
  MigrationProgress({
    required this.currentStep,
    required this.totalSteps,
    required this.currentOperation,
    required this.progress,
    this.error,
  });
}

/// Migration handler function type
typedef MigrationHandler = Future<Map<String, dynamic>> Function(Map<String, dynamic> oldSettings);

/// Settings schema versions and their changes
class SettingsSchema {
  static const int version1 = 1; // Initial version
  static const int version2 = 2; // Added messaging settings
  static const int version3 = 3; // Added e-commerce settings
  static const int version4 = 4; // Added privacy enhancements
  static const int version5 = 5; // Added accessibility settings
  
  static const int currentVersion = version5;
  
  /// Get schema changes for a version
  static List<String> getChangesForVersion(int version) {
    switch (version) {
      case version2:
        return [
          'Added messaging preferences',
          'Added read receipts setting',
          'Added typing indicators setting',
          'Added message encryption setting',
        ];
      case version3:
        return [
          'Added e-commerce settings',
          'Added payment preferences',
          'Added shipping settings',
          'Added order notifications',
        ];
      case version4:
        return [
          'Enhanced privacy settings',
          'Added two-factor authentication',
          'Added biometric login',
          'Added auto-lock settings',
        ];
      case version5:
        return [
          'Added accessibility features',
          'Added high contrast mode',
          'Added screen reader support',
          'Added haptic feedback settings',
        ];
      default:
        return [];
    }
  }
  
  /// Get required migrations for version upgrade
  static List<MigrationPath> getMigrationPaths(int fromVersion, int toVersion) {
    final paths = <MigrationPath>[];
    
    for (int version = fromVersion + 1; version <= toVersion; version++) {
      paths.add(MigrationPath(
        fromVersion: version - 1,
        toVersion: version,
        description: 'Migrate to version $version',
        changes: getChangesForVersion(version),
      ));
    }
    
    return paths;
  }
}
