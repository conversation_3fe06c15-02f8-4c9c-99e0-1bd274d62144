import 'package:flutter/material.dart';
import 'app_services_interface.dart';
import '../../data/models/app_settings_model.dart';
import '../../data/models/identity_settings_model.dart';

/// Interface for comprehensive settings management
///
/// This service manages both app-global settings and identity-specific settings.
/// App-global settings apply to the entire application regardless of identity.
/// Identity-specific settings can override global defaults for each user identity.
abstract class SettingsServiceAbstract extends InitializableServiceAbstract {
  // ========================================
  // APP-GLOBAL SETTINGS OPERATIONS
  // ========================================

  /// Get current app-global settings
  Future<AppSettings> getAppSettings();

  /// Update app-global settings
  Future<void> updateAppSettings(AppSettings settings);

  /// Stream of app-global settings changes
  Stream<AppSettings> get appSettingsStream;

  /// Load app-global settings from persistent storage
  Future<void> loadAppSettings();

  /// Save app-global settings to persistent storage
  Future<void> saveAppSettings();

  /// Reset app-global settings to default values
  Future<void> resetAppSettings();

  // ========================================
  // IDENTITY-SPECIFIC SETTINGS OPERATIONS
  // ========================================

  /// Get settings for a specific identity
  Future<IdentitySettings> getIdentitySettings(String myIdentityCardId);

  /// Update settings for a specific identity
  Future<void> updateIdentitySettings(IdentitySettings settings);

  /// Stream of identity settings changes for a specific identity
  Stream<IdentitySettings> getIdentitySettingsStream(String myIdentityCardId);

  /// Get settings for all identities of the current user
  Future<List<IdentitySettings>> getAllIdentitySettings();

  /// Create default settings for a new identity
  Future<IdentitySettings> createIdentitySettings(
    String myIdentityCardId, {
    bool isBusinessIdentity = false,
  });

  /// Delete settings for an identity (when identity is deleted)
  Future<void> deleteIdentitySettings(String myIdentityCardId);

  /// Reset identity settings to defaults
  Future<void> resetIdentitySettings(String myIdentityCardId);

  // ========================================
  // THEME AND APPEARANCE SETTINGS
  // ========================================

  /// Get current dark mode setting
  bool get isDarkMode;

  /// Enable or disable dark mode
  Future<void> setDarkMode(bool enabled);

  /// Get current theme mode (light, dark, system)
  ThemeMode get themeMode;

  /// Set theme mode
  Future<void> setThemeMode(ThemeMode mode);

  // ========================================
  // NOTIFICATION SETTINGS
  // ========================================

  /// Get notification enabled status
  bool get notificationsEnabled;

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled);

  /// Get push notification enabled status
  bool get pushNotificationsEnabled;

  /// Enable or disable push notifications
  Future<void> setPushNotificationsEnabled(bool enabled);

  // ========================================
  // BACKGROUND WORK SETTINGS
  // ========================================

  /// Get background work enabled status
  bool get backgroundWorkEnabled;

  /// Enable or disable background work
  Future<void> setBackgroundWorkEnabled(bool enabled);

  // ========================================
  // PRIVACY SETTINGS
  // ========================================

  /// Get analytics enabled status
  bool get analyticsEnabled;

  /// Enable or disable analytics
  Future<void> setAnalyticsEnabled(bool enabled);

  /// Get crash reporting enabled status
  bool get crashReportingEnabled;

  /// Enable or disable crash reporting
  Future<void> setCrashReportingEnabled(bool enabled);

  // ========================================
  // APP BEHAVIOR SETTINGS
  // ========================================

  /// Get auto-save enabled status
  bool get autoSaveEnabled;

  /// Enable or disable auto-save
  Future<void> setAutoSaveEnabled(bool enabled);

  /// Get auto-save interval
  Duration get autoSaveInterval;

  /// Set auto-save interval
  Future<void> setAutoSaveInterval(Duration interval);

  // ========================================
  // GENERIC SETTING OPERATIONS
  // ========================================

  /// Get a setting value by key with optional default
  T? getSetting<T>(String key, {T? defaultValue});

  /// Set a setting value by key
  Future<void> setSetting<T>(String key, T value);

  /// Remove a setting by key
  Future<void> removeSetting(String key);

  /// Check if a setting exists
  bool hasSetting(String key);

  // ========================================
  // BULK OPERATIONS
  // ========================================

  /// Get all settings as a map
  Map<String, dynamic> getAllSettings();

  /// Set multiple settings at once
  Future<void> setMultipleSettings(Map<String, dynamic> settings);

  /// Clear all settings (reset to defaults)
  Future<void> clearAllSettings();

  // ========================================
  // SETTINGS VALIDATION
  // ========================================

  /// Validate all current settings
  bool validateSettings();

  /// Get list of invalid setting keys
  List<String> getInvalidSettings();

  // ========================================
  // SETTINGS EXPORT/IMPORT
  // ========================================

  /// Export all settings to JSON string
  Future<String> exportSettings();

  /// Import settings from JSON string
  /// Returns true if import was successful
  Future<bool> importSettings(String settingsJson);

  // ========================================
  // SETTINGS CHANGE NOTIFICATIONS
  // ========================================

  /// Stream of setting changes (key-value pairs)
  Stream<Map<String, dynamic>> get settingsChanges;

  /// Add a listener for specific setting changes
  void addSettingChangeListener(String key, Function(dynamic value) listener);

  /// Remove a setting change listener
  void removeSettingChangeListener(
    String key,
    Function(dynamic value) listener,
  );

  // ========================================
  // ENHANCED SETTINGS MANAGEMENT
  // ========================================

  /// Export all settings (app + all identities)
  Future<String> exportAllSettings();

  /// Export app-global settings only
  Future<String> exportAppSettings();

  /// Export identity settings only
  Future<String> exportIdentitySettings(String myIdentityCardId);

  /// Import comprehensive settings from exported data
  Future<void> importAllSettings(String settingsData);

  /// Validate comprehensive settings data
  Future<bool> validateAllSettings(String settingsData);

  /// Get settings metadata
  Future<SettingsMetadata> getSettingsMetadata();
}

// ========================================
// SUPPORTING CLASSES AND ENUMS
// ========================================

/// Settings change event
class SettingsChangeEvent {
  final SettingsChangeType type;
  final String settingKey;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;
  final String? identityId; // null for app-global settings

  SettingsChangeEvent({
    required this.type,
    required this.settingKey,
    required this.oldValue,
    required this.newValue,
    required this.timestamp,
    this.identityId,
  });
}

/// Settings change types
enum SettingsChangeType { appGlobal, identity }

/// Settings metadata
class SettingsMetadata {
  final DateTime lastModified;
  final DateTime createdAt;
  final int appSettingsVersion;
  final int identitySettingsVersion;
  final String? deviceId;
  final int totalIdentities;

  SettingsMetadata({
    required this.lastModified,
    required this.createdAt,
    required this.appSettingsVersion,
    required this.identitySettingsVersion,
    this.deviceId,
    required this.totalIdentities,
  });
}
