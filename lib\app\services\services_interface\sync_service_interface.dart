import 'app_services_interface.dart';

/// Interface for sync service - handles data synchronization
abstract class SyncServiceAbstract extends InitializableServiceAbstract {
  Future<void> startSync();
  Future<void> stopSync();
  Future<void> syncNow();
  Future<void> syncRoom(String roomId);
  Future<void> syncMessages(String roomId, {DateTime? since});
  Future<void> syncUserProfile();
  Future<void> syncContacts();
  Stream<SyncStatus> get syncStatusStream;
  Future<void> handleIncomingMessage(Map<String, dynamic> messageData);
  Future<void> handleRoomUpdate(Map<String, dynamic> roomData);
  Future<void> handleUserUpdate(Map<String, dynamic> userData);
  Future<void> schedulePeriodicSync(Duration interval);
  void dispose();
}

// Supporting classes
enum SyncStatus { idle, syncing, completed, error, paused }

class SyncProgress {
  final SyncStatus status;
  final double progress;
  final String? currentItem;
  final String? error;
  final DateTime timestamp;
  
  SyncProgress({
    required this.status,
    this.progress = 0.0,
    this.currentItem,
    this.error,
    required this.timestamp,
  });
}
