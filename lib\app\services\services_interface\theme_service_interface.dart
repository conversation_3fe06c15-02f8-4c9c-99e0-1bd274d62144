import 'package:flutter/material.dart';
import 'app_services_interface.dart';

/// Interface for theme service - handles app theming and appearance
abstract class ThemeServiceAbstract extends InitializableServiceAbstract {
  Future<void> setTheme(ThemeMode mode);
  ThemeMode getCurrentTheme();
  Stream<ThemeMode> get themeStream;
  Future<void> setCustomTheme(CustomTheme theme);
  Future<List<CustomTheme>> getAvailableThemes();
  Future<void> setAccentColor(Color color);
  Color getAccentColor();
  void dispose();
}

// Supporting classes
class CustomTheme {
  final String id;
  final String name;
  final String? description;
  final ThemeData lightTheme;
  final ThemeData darkTheme;
  final Color primaryColor;
  final Color accentColor;
  final bool isBuiltIn;
  final String? previewImageUrl;
  
  CustomTheme({
    required this.id,
    required this.name,
    this.description,
    required this.lightTheme,
    required this.darkTheme,
    required this.primaryColor,
    required this.accentColor,
    this.isBuiltIn = false,
    this.previewImageUrl,
  });
}

class ThemeConfiguration {
  final ThemeMode mode;
  final String? customThemeId;
  final Color? accentColor;
  final double? fontSize;
  final bool useSystemAccentColor;
  final bool followSystemTheme;
  
  ThemeConfiguration({
    required this.mode,
    this.customThemeId,
    this.accentColor,
    this.fontSize,
    this.useSystemAccentColor = false,
    this.followSystemTheme = true,
  });
}

class ColorScheme {
  final Color primary;
  final Color secondary;
  final Color surface;
  final Color background;
  final Color error;
  final Color onPrimary;
  final Color onSecondary;
  final Color onSurface;
  final Color onBackground;
  final Color onError;
  
  ColorScheme({
    required this.primary,
    required this.secondary,
    required this.surface,
    required this.background,
    required this.error,
    required this.onPrimary,
    required this.onSecondary,
    required this.onSurface,
    required this.onBackground,
    required this.onError,
  });
}
