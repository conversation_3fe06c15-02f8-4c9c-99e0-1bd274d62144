import 'app_services_interface.dart';

/// Interface for translation service - handles text translation and language detection
abstract class TranslationServiceAbstract extends InitializableServiceAbstract {
  Future<String> translateText(String text, String targetLanguage);
  Future<String> detectLanguage(String text);
  Future<List<String>> getSupportedLanguages();
  Future<void> downloadLanguagePack(String languageCode);
  Future<bool> isLanguagePackDownloaded(String languageCode);
  Future<String> translateMessage(String messageId, String targetLanguage);
  void dispose();
}

// Supporting classes
enum TranslationProvider { google, microsoft, amazon, local }

class TranslationResult {
  final String originalText;
  final String translatedText;
  final String sourceLanguage;
  final String targetLanguage;
  final double confidence;
  final TranslationProvider provider;
  final DateTime timestamp;
  
  TranslationResult({
    required this.originalText,
    required this.translatedText,
    required this.sourceLanguage,
    required this.targetLanguage,
    required this.confidence,
    required this.provider,
    required this.timestamp,
  });
}

class LanguageDetectionResult {
  final String detectedLanguage;
  final double confidence;
  final List<LanguageMatch> alternatives;
  
  LanguageDetectionResult({
    required this.detectedLanguage,
    required this.confidence,
    this.alternatives = const [],
  });
}

class LanguageMatch {
  final String languageCode;
  final String languageName;
  final double confidence;
  
  LanguageMatch({
    required this.languageCode,
    required this.languageName,
    required this.confidence,
  });
}

class LanguagePack {
  final String languageCode;
  final String languageName;
  final String version;
  final int size;
  final bool isDownloaded;
  final DateTime? downloadedAt;
  final DateTime? lastUpdated;
  
  LanguagePack({
    required this.languageCode,
    required this.languageName,
    required this.version,
    required this.size,
    this.isDownloaded = false,
    this.downloadedAt,
    this.lastUpdated,
  });
}
