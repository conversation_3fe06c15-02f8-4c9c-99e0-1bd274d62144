import 'app_services_interface.dart';

/// Interface for voice service - handles voice recording and audio playback
abstract class VoiceServiceAbstract extends InitializableServiceAbstract {
  Future<bool> startRecording(String outputPath);
  Future<String?> stopRecording();
  Future<void> pauseRecording();
  Future<void> resumeRecording();
  Future<void> cancelRecording();
  Future<void> playAudio(String audioPath);
  Future<void> pauseAudio();
  Future<void> stopAudio();
  Stream<RecordingState> get recordingStateStream;
  Stream<AudioPlaybackState> get playbackStateStream;
  Future<Duration> getAudioDuration(String audioPath);
  void dispose();
}

// Supporting classes
enum RecordingState { idle, recording, paused, stopped, error }

enum AudioPlaybackState { idle, playing, paused, stopped, loading, error }

enum AudioQuality { low, medium, high, lossless }

class RecordingInfo {
  final String path;
  final Duration duration;
  final int fileSize;
  final AudioQuality quality;
  final DateTime startTime;
  final DateTime? endTime;
  
  RecordingInfo({
    required this.path,
    required this.duration,
    required this.fileSize,
    required this.quality,
    required this.startTime,
    this.endTime,
  });
}

class AudioPlaybackInfo {
  final String path;
  final Duration duration;
  final Duration currentPosition;
  final double volume;
  final double playbackSpeed;
  final bool isLooping;
  
  AudioPlaybackInfo({
    required this.path,
    required this.duration,
    required this.currentPosition,
    this.volume = 1.0,
    this.playbackSpeed = 1.0,
    this.isLooping = false,
  });
}

class VoiceRecordingSettings {
  final AudioQuality quality;
  final int sampleRate;
  final int bitRate;
  final bool enableNoiseReduction;
  final bool enableEchoCancellation;
  final Duration? maxDuration;
  
  VoiceRecordingSettings({
    this.quality = AudioQuality.medium,
    this.sampleRate = 44100,
    this.bitRate = 128000,
    this.enableNoiseReduction = true,
    this.enableEchoCancellation = true,
    this.maxDuration,
  });
}
