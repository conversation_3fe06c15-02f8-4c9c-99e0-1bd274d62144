import 'app_services_interface.dart';
import 'presence_service_interface.dart';

/// Interface for WebSocket service - handles real-time communication
abstract class WebSocketServiceAbstract extends InitializableServiceAbstract {
  Future<bool> connect();
  Future<void> disconnect();
  Future<void> reconnect();
  bool get isConnected;
  Stream<ConnectionStatus> get connectionStatusStream;
  Stream<WebSocketEvent> get eventStream;
  Future<void> sendMessage(WebSocketMessage message);
  Future<void> joinRoom(String roomId);
  Future<void> leaveRoom(String roomId);
  Future<void> sendTypingIndicator(String roomId, bool isTyping);
  Future<void> sendReadReceipt(String messageId);
  Future<void> sendPresenceUpdate(PresenceStatus status);
  void dispose();
}

// Supporting classes
enum ConnectionStatus {
  connected,
  disconnected,
  connecting,
  reconnecting,
  error,
}

// Note: PresenceStatus enum is imported from presence_service_interface.dart

class WebSocketEvent {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  WebSocketEvent({
    required this.type,
    required this.data,
    required this.timestamp,
  });
}

class WebSocketMessage {
  final String type;
  final Map<String, dynamic> data;
  final String? roomId;
  WebSocketMessage({required this.type, required this.data, this.roomId});
}
