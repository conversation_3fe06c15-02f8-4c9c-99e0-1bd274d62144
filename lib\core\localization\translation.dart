import 'package:refreshed/get_navigation/src/root/internationalization.dart';

class AppTranslation extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'en_US': {
      // App Info
      'title': '<PERSON>wan',
      'appName': '<PERSON>wan',
      'appDescription': 'Secure Communication Platform',

      // Navigation & General
      'home': 'Home',
      'contact': 'Contact',
      'contacts': 'Contacts',
      'settings': 'Settings',
      'profile': 'Profile',
      'about': 'About',
      'help': 'Help',
      'back': 'Back',
      'next': 'Next',
      'previous': 'Previous',
      'done': 'Done',
      'save': 'Save',
      'cancel': 'Cancel',
      'delete': 'Delete',
      'edit': 'Edit',
      'add': 'Add',
      'remove': 'Remove',
      'update': 'Update',
      'refresh': 'Refresh',
      'retry': 'Retry',
      'loading': 'Loading...',
      'ongoing': 'Ongoing...',
      'completed': 'Completed',
      'failed': 'Failed',
      'success': 'Success',
      'error': 'Error',
      'warning': 'Warning',
      'info': 'Information',
      'confirm': 'Confirm',
      'yes': 'Yes',
      'no': 'No',
      'ok': 'OK',
      'close': 'Close',
      'open': 'Open',
      'copy': 'Copy',
      'copied': 'Copied',
      'paste': 'Paste',
      'share': 'Share',
      'send': 'Send',
      'receive': 'Receive',
      'download': 'Download',
      'upload': 'Upload',
      'search': 'Search',
      'searchHere': 'Search here',
      'filter': 'Filter',
      'sort': 'Sort',
      'clear': 'Clear',
      'reset': 'Reset',
      'apply': 'Apply',
      'select': 'Select',
      'selectAll': 'Select All',
      'deselectAll': 'Deselect All',
      'none': 'None',
      'all': 'All',
      'more': 'More',
      'less': 'Less',
      'show': 'Show',
      'hide': 'Hide',
      'view': 'View',
      'preview': 'Preview',
      'details': 'Details',
      'options': 'Options',
      'preferences': 'Preferences',
      'advanced': 'Advanced',
      'basic': 'Basic',
      'custom': 'Custom',
      'default': 'Default',
      'auto': 'Auto',
      'manual': 'Manual',
      'enable': 'Enable',
      'disable': 'Disable',
      'enabled': 'Enabled',
      'disabled': 'Disabled',
      'on': 'On',
      'off': 'Off',
      'online': 'Online',
      'offline': 'Offline',
      'connected': 'Connected',
      'disconnected': 'Disconnected',
      'connecting': 'Connecting...',
      'reconnecting': 'Reconnecting...',

      // Welcome & Onboarding
      'welcomeToDeewan': 'Welcome to Deewan',
      'getStarted': 'Get Started',
      'skipIntro': 'Skip Introduction',
      'continueSetup': 'Continue Setup',
      'finishSetup': 'Finish Setup',

      // Authentication
      'login': 'Login',
      'logout': 'Logout',
      'register': 'Register',
      'signIn': 'Sign In',
      'signUp': 'Sign Up',
      'signOut': 'Sign Out',
      'email': 'Email',
      'emailAddress': 'Email Address',
      'password': 'Password',
      'confirmPassword': 'Confirm Password',
      'currentPassword': 'Current Password',
      'newPassword': 'New Password',
      'forgotPassword': 'Forgot Password?',
      'resetPassword': 'Reset Password',
      'changePassword': 'Change Password',
      'createAccount': 'Create Account',
      'haveAccount': 'Already have an account?',
      'noAccount': "Don't have an account?",
      'rememberMe': 'Remember Me',
      'staySignedIn': 'Stay Signed In',
      'biometricLogin': 'Biometric Login',
      'faceId': 'Face ID',
      'touchId': 'Touch ID',
      'fingerprint': 'Fingerprint',
      'pinCode': 'PIN Code',
      'passcode': 'Passcode',
      'securityCode': 'Security Code',
      'verificationCode': 'Verification Code',
      'twoFactorAuth': 'Two-Factor Authentication',
      'phoneVerification': 'Phone Verification',
      'emailVerification': 'Email Verification',
      'accountVerification': 'Account Verification',
      'verifyAccount': 'Verify Account',
      'resendCode': 'Resend Code',
      'codeExpired': 'Code Expired',
      'invalidCode': 'Invalid Code',
      'codeResent': 'Code Resent',
      'accountCreated': 'Account Created Successfully',
      'loginSuccessful': 'Login Successful',
      'logoutSuccessful': 'Logout Successful',
      'passwordChanged': 'Password Changed Successfully',
      'passwordReset': 'Password Reset Successfully',
      'accountDeleted': 'Account Deleted',
      'accountSuspended': 'Account Suspended',
      'accountLocked': 'Account Locked',
      'sessionExpired': 'Session Expired',
      'unauthorized': 'Unauthorized Access',
      'accessDenied': 'Access Denied',

      // Settings & Preferences
      'language': 'English',
      'languageSettings': 'Language Settings',
      'changeLanguage': 'Change Language',
      'selectLanguage': 'Select Language',
      'currentLanguage': 'Current Language',
      'systemLanguage': 'System Language',
      'arabic': 'Arabic',
      'english': 'English',
      'theme': 'Theme',
      'themeSettings': 'Theme Settings',
      'darkMode': 'Dark Mode',
      'lightMode': 'Light Mode',
      'systemTheme': 'System Theme',
      'autoTheme': 'Auto Theme',
      'appearance': 'Appearance',
      'display': 'Display',
      'fontSize': 'Font Size',
      'fontFamily': 'Font Family',
      'textSize': 'Text Size',
      'small': 'Small',
      'medium': 'Medium',
      'large': 'Large',
      'extraLarge': 'Extra Large',
      'notifications': 'Notifications',
      'notificationSettings': 'Notification Settings',
      'pushNotifications': 'Push Notifications',
      'emailNotifications': 'Email Notifications',
      'smsNotifications': 'SMS Notifications',
      'soundNotifications': 'Sound Notifications',
      'vibrationNotifications': 'Vibration Notifications',
      'notificationSound': 'Notification Sound',
      'ringtone': 'Ringtone',
      'volume': 'Volume',
      'privacy': 'Privacy',
      'privacySettings': 'Privacy Settings',
      'security': 'Security',
      'securitySettings': 'Security Settings',
      'dataUsage': 'Data Usage',
      'storage': 'Storage',
      'backup': 'Backup',
      'restore': 'Restore',
      'export': 'Export',
      'import': 'Import',
      'sync': 'Sync',
      'autoSync': 'Auto Sync',
      'manualSync': 'Manual Sync',
      'lastSync': 'Last Sync',
      'syncNow': 'Sync Now',
      'syncSettings': 'Sync Settings',
      'accountSettings': 'Account Settings',
      'profileSettings': 'Profile Settings',
      'generalSettings': 'General Settings',
      'advancedSettings': 'Advanced Settings',
      'developerOptions': 'Developer Options',
      'debugMode': 'Debug Mode',
      'testMode': 'Test Mode',
      'version': 'Version',
      'buildNumber': 'Build Number',
      'appVersion': 'App Version',
      'systemInfo': 'System Information',
      'deviceInfo': 'Device Information',
      'aboutApp': 'About App',
      'termsOfService': 'Terms of Service',
      'privacyPolicy': 'Privacy Policy',
      'licenses': 'Licenses',
      'openSourceLicenses': 'Open Source Licenses',
      'contactUs': 'Contact Us',
      'support': 'Support',
      'feedback': 'Feedback',
      'reportBug': 'Report Bug',
      'requestFeature': 'Request Feature',
      'rateApp': 'Rate App',
      'shareApp': 'Share App',
      'recommendApp': 'Recommend App',
      'checkUpdates': 'Check for Updates',
      'updateAvailable': 'Update Available',
      'updateRequired': 'Update Required',
      'updateNow': 'Update Now',
      'updateLater': 'Update Later',
      'noUpdates': 'No Updates Available',
      'downloading': 'Downloading...',
      'installing': 'Installing...',
      'updateComplete': 'Update Complete',
      'updateFailed': 'Update Failed',
      'restartRequired': 'Restart Required',
      'restartNow': 'Restart Now',
      'restartLater': 'Restart Later',

      // Validation Messages
      'fieldRequired': 'This field is required',
      'invalidEmail': 'Please enter a valid email address',
      'invalidPassword': 'Password must be at least 8 characters',
      'passwordMismatch': 'Passwords do not match',
      'invalidPhoneNumber': 'Please enter a valid phone number',
      'invalidUrl': 'Please enter a valid URL',
      'invalidDate': 'Please enter a valid date',
      'invalidTime': 'Please enter a valid time',
      'invalidNumber': 'Please enter a valid number',
      'invalidInteger': 'Please enter a valid integer',
      'minLength': 'Minimum length is {0} characters',
      'maxLength': 'Maximum length is {0} characters',
      'minValue': 'Minimum value is {0}',
      'maxValue': 'Maximum value is {0}',
      'invalidAge': 'Please enter a valid age',
      'mustBeAtLeast18': 'You must be at least 18 years old',
      'invalidOption': 'Please select a valid option',
      'fileRequired': 'Please select a file',
      'invalidFileType': 'Invalid file type',
      'fileTooLarge': 'File size is too large',
      'networkError': 'Network connection error',
      'serverError': 'Server error occurred',
      'unknownError': 'An unknown error occurred',
      'timeoutError': 'Request timeout',
      'permissionDenied': 'Permission denied',
      'locationNotFound': 'Location not found',
      'cameraNotAvailable': 'Camera not available',
      'microphoneNotAvailable': 'Microphone not available',
      'storageNotAvailable': 'Storage not available',
      'internetRequired': 'Internet connection required',
      'gpsRequired': 'GPS location required',
      'bluetoothRequired': 'Bluetooth required',
      'nfcRequired': 'NFC required',
      'biometricNotAvailable': 'Biometric authentication not available',
      'biometricNotEnrolled': 'No biometric data enrolled',
      'deviceNotSupported': 'Device not supported',
      'featureNotAvailable': 'Feature not available',
      'serviceUnavailable': 'Service unavailable',
      'maintenanceMode': 'System under maintenance',
      'quotaExceeded': 'Quota exceeded',
      'rateLimitExceeded': 'Rate limit exceeded',
      'subscriptionRequired': 'Subscription required',
      'paymentRequired': 'Payment required',
      'trialExpired': 'Trial period expired',
      'accountNotVerified': 'Account not verified',
      'emailNotVerified': 'Email not verified',
      'phoneNotVerified': 'Phone not verified',
      'documentNotVerified': 'Document not verified',
      'identityNotVerified': 'Identity not verified',
      'ageVerificationRequired': 'Age verification required',
      'parentalConsentRequired': 'Parental consent required',
      'termsNotAccepted': 'Terms of service not accepted',
      'privacyPolicyNotAccepted': 'Privacy policy not accepted',
      'cookiesNotAccepted': 'Cookies not accepted',
      'locationPermissionRequired': 'Location permission required',
      'cameraPermissionRequired': 'Camera permission required',
      'microphonePermissionRequired': 'Microphone permission required',
      'storagePermissionRequired': 'Storage permission required',
      'contactsPermissionRequired': 'Contacts permission required',
      'calendarPermissionRequired': 'Calendar permission required',
      'notificationPermissionRequired': 'Notification permission required',

      // Additional UI Elements
      'textDirection': 'Text Direction',
      'rtl': 'Right to Left',
      'ltr': 'Left to Right',
      'backgroundWorkEnabled': 'Background Work',
    },
    'ar_SA': {
      // App Info
      'title': 'ديوان',
      'appName': 'ديوان',
      'appDescription': 'منصة التواصل الآمن',

      // Navigation & General
      'home': 'الرئيسية',
      'contact': 'جهة الاتصال',
      'contacts': 'جهات الاتصال',
      'settings': 'الإعدادات',
      'profile': 'الملف الشخصي',
      'about': 'حول',
      'help': 'المساعدة',
      'back': 'رجوع',
      'next': 'التالي',
      'previous': 'السابق',
      'done': 'تم',
      'save': 'حفظ',
      'cancel': 'إلغاء',
      'delete': 'حذف',
      'edit': 'تعديل',
      'add': 'إضافة',
      'remove': 'إزالة',
      'update': 'تحديث',
      'refresh': 'تحديث',
      'retry': 'إعادة المحاولة',
      'loading': 'جاري التحميل...',
      'ongoing': 'جاري التنفيذ...',
      'completed': 'مكتمل',
      'failed': 'فشل',
      'success': 'نجح',
      'error': 'خطأ',
      'warning': 'تحذير',
      'info': 'معلومات',
      'confirm': 'تأكيد',
      'yes': 'نعم',
      'no': 'لا',
      'ok': 'موافق',
      'close': 'إغلاق',
      'open': 'فتح',
      'copy': 'نسخ',
      'copied': 'تم النسخ',
      'paste': 'لصق',
      'share': 'مشاركة',
      'send': 'إرسال',
      'receive': 'استقبال',
      'download': 'تحميل',
      'upload': 'رفع',
      'search': 'البحث',
      'searchHere': 'ابحث هنا',
      'filter': 'تصفية',
      'sort': 'ترتيب',
      'clear': 'مسح',
      'reset': 'إعادة تعيين',
      'apply': 'تطبيق',
      'select': 'اختيار',
      'selectAll': 'اختيار الكل',
      'deselectAll': 'إلغاء اختيار الكل',
      'none': 'لا شيء',
      'all': 'الكل',
      'more': 'المزيد',
      'less': 'أقل',
      'show': 'إظهار',
      'hide': 'إخفاء',
      'view': 'عرض',
      'preview': 'معاينة',
      'details': 'التفاصيل',
      'options': 'الخيارات',
      'preferences': 'التفضيلات',
      'advanced': 'متقدم',
      'basic': 'أساسي',
      'custom': 'مخصص',
      'default': 'افتراضي',
      'auto': 'تلقائي',
      'manual': 'يدوي',
      'enable': 'تفعيل',
      'disable': 'تعطيل',
      'enabled': 'مفعل',
      'disabled': 'معطل',
      'on': 'تشغيل',
      'off': 'إيقاف',
      'online': 'متصل',
      'offline': 'غير متصل',
      'connected': 'متصل',
      'disconnected': 'منقطع',
      'connecting': 'جاري الاتصال...',
      'reconnecting': 'جاري إعادة الاتصال...',

      // Welcome & Onboarding
      'welcomeToDeewan': 'مرحباً بك في ديوان',
      'getStarted': 'ابدأ الآن',
      'skipIntro': 'تخطي المقدمة',
      'continueSetup': 'متابعة الإعداد',
      'finishSetup': 'إنهاء الإعداد',

      // Authentication
      'login': 'تسجيل الدخول',
      'logout': 'تسجيل الخروج',
      'register': 'تسجيل',
      'signIn': 'تسجيل الدخول',
      'signUp': 'إنشاء حساب',
      'signOut': 'تسجيل الخروج',
      'email': 'البريد الإلكتروني',
      'emailAddress': 'عنوان البريد الإلكتروني',
      'password': 'كلمة المرور',
      'confirmPassword': 'تأكيد كلمة المرور',
      'currentPassword': 'كلمة المرور الحالية',
      'newPassword': 'كلمة المرور الجديدة',
      'forgotPassword': 'نسيت كلمة المرور؟',
      'resetPassword': 'إعادة تعيين كلمة المرور',
      'changePassword': 'تغيير كلمة المرور',
      'createAccount': 'إنشاء حساب',
      'haveAccount': 'لديك حساب بالفعل؟',
      'noAccount': 'ليس لديك حساب؟',
      'rememberMe': 'تذكرني',
      'staySignedIn': 'البقاء مسجلاً',
      'biometricLogin': 'تسجيل الدخول البيومتري',
      'faceId': 'معرف الوجه',
      'touchId': 'معرف اللمس',
      'fingerprint': 'بصمة الإصبع',
      'pinCode': 'رمز PIN',
      'passcode': 'رمز المرور',
      'securityCode': 'رمز الأمان',
      'verificationCode': 'رمز التحقق',
      'twoFactorAuth': 'المصادقة الثنائية',
      'phoneVerification': 'التحقق من الهاتف',
      'emailVerification': 'التحقق من البريد الإلكتروني',
      'accountVerification': 'التحقق من الحساب',
      'verifyAccount': 'تحقق من الحساب',
      'resendCode': 'إعادة إرسال الرمز',
      'codeExpired': 'انتهت صلاحية الرمز',
      'invalidCode': 'رمز غير صحيح',
      'codeResent': 'تم إعادة إرسال الرمز',
      'accountCreated': 'تم إنشاء الحساب بنجاح',
      'loginSuccessful': 'تم تسجيل الدخول بنجاح',
      'logoutSuccessful': 'تم تسجيل الخروج بنجاح',
      'passwordChanged': 'تم تغيير كلمة المرور بنجاح',
      'passwordReset': 'تم إعادة تعيين كلمة المرور بنجاح',
      'accountDeleted': 'تم حذف الحساب',
      'accountSuspended': 'تم تعليق الحساب',
      'accountLocked': 'تم قفل الحساب',
      'sessionExpired': 'انتهت صلاحية الجلسة',
      'unauthorized': 'وصول غير مصرح',
      'accessDenied': 'تم رفض الوصول',

      // Settings & Preferences
      'language': 'العربية',
      'languageSettings': 'إعدادات اللغة',
      'changeLanguage': 'تغيير اللغة',
      'selectLanguage': 'اختر اللغة',
      'currentLanguage': 'اللغة الحالية',
      'systemLanguage': 'لغة النظام',
      'arabic': 'العربية',
      'english': 'الإنجليزية',
      'theme': 'المظهر',
      'themeSettings': 'إعدادات المظهر',
      'darkMode': 'الوضع المظلم',
      'lightMode': 'الوضع الفاتح',
      'systemTheme': 'مظهر النظام',
      'autoTheme': 'مظهر تلقائي',
      'appearance': 'المظهر',
      'display': 'العرض',
      'fontSize': 'حجم الخط',
      'fontFamily': 'نوع الخط',
      'textSize': 'حجم النص',
      'small': 'صغير',
      'medium': 'متوسط',
      'large': 'كبير',
      'extraLarge': 'كبير جداً',
      'notifications': 'الإشعارات',
      'notificationSettings': 'إعدادات الإشعارات',
      'pushNotifications': 'الإشعارات الفورية',
      'emailNotifications': 'إشعارات البريد الإلكتروني',
      'smsNotifications': 'إشعارات الرسائل النصية',
      'soundNotifications': 'إشعارات صوتية',
      'vibrationNotifications': 'إشعارات الاهتزاز',
      'notificationSound': 'صوت الإشعار',
      'ringtone': 'نغمة الرنين',
      'volume': 'مستوى الصوت',
      'privacy': 'الخصوصية',
      'privacySettings': 'إعدادات الخصوصية',
      'security': 'الأمان',
      'securitySettings': 'إعدادات الأمان',
      'dataUsage': 'استخدام البيانات',
      'storage': 'التخزين',
      'backup': 'النسخ الاحتياطي',
      'restore': 'الاستعادة',
      'export': 'تصدير',
      'import': 'استيراد',
      'sync': 'المزامنة',
      'autoSync': 'مزامنة تلقائية',
      'manualSync': 'مزامنة يدوية',
      'lastSync': 'آخر مزامنة',
      'syncNow': 'مزامنة الآن',
      'syncSettings': 'إعدادات المزامنة',
      'accountSettings': 'إعدادات الحساب',
      'profileSettings': 'إعدادات الملف الشخصي',
      'generalSettings': 'الإعدادات العامة',
      'advancedSettings': 'الإعدادات المتقدمة',
      'developerOptions': 'خيارات المطور',
      'debugMode': 'وضع التصحيح',
      'testMode': 'وضع الاختبار',
      'version': 'الإصدار',
      'buildNumber': 'رقم البناء',
      'appVersion': 'إصدار التطبيق',
      'systemInfo': 'معلومات النظام',
      'deviceInfo': 'معلومات الجهاز',
      'aboutApp': 'حول التطبيق',
      'termsOfService': 'شروط الخدمة',
      'privacyPolicy': 'سياسة الخصوصية',
      'licenses': 'التراخيص',
      'openSourceLicenses': 'تراخيص المصدر المفتوح',
      'contactUs': 'اتصل بنا',
      'support': 'الدعم',
      'feedback': 'التعليقات',
      'reportBug': 'الإبلاغ عن خطأ',
      'requestFeature': 'طلب ميزة',
      'rateApp': 'قيم التطبيق',
      'shareApp': 'شارك التطبيق',
      'recommendApp': 'أوصي بالتطبيق',
      'checkUpdates': 'التحقق من التحديثات',
      'updateAvailable': 'تحديث متاح',
      'updateRequired': 'تحديث مطلوب',
      'updateNow': 'تحديث الآن',
      'updateLater': 'تحديث لاحقاً',
      'noUpdates': 'لا توجد تحديثات متاحة',
      'downloading': 'جاري التحميل...',
      'installing': 'جاري التثبيت...',
      'updateComplete': 'اكتمل التحديث',
      'updateFailed': 'فشل التحديث',
      'restartRequired': 'إعادة التشغيل مطلوبة',
      'restartNow': 'إعادة التشغيل الآن',
      'restartLater': 'إعادة التشغيل لاحقاً',

      // Validation Messages
      'fieldRequired': 'هذا الحقل مطلوب',
      'invalidEmail': 'يرجى إدخال عنوان بريد إلكتروني صحيح',
      'invalidPassword': 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
      'passwordMismatch': 'كلمات المرور غير متطابقة',
      'invalidPhoneNumber': 'يرجى إدخال رقم هاتف صحيح',
      'invalidUrl': 'يرجى إدخال رابط صحيح',
      'invalidDate': 'يرجى إدخال تاريخ صحيح',
      'invalidTime': 'يرجى إدخال وقت صحيح',
      'invalidNumber': 'يرجى إدخال رقم صحيح',
      'invalidInteger': 'يرجى إدخال رقم صحيح',
      'minLength': 'الحد الأدنى للطول هو {0} أحرف',
      'maxLength': 'الحد الأقصى للطول هو {0} أحرف',
      'minValue': 'الحد الأدنى للقيمة هو {0}',
      'maxValue': 'الحد الأقصى للقيمة هو {0}',
      'invalidAge': 'يرجى إدخال عمر صحيح',
      'mustBeAtLeast18': 'يجب أن تكون 18 سنة على الأقل',
      'invalidOption': 'يرجى اختيار خيار صحيح',
      'fileRequired': 'يرجى اختيار ملف',
      'invalidFileType': 'نوع الملف غير صحيح',
      'fileTooLarge': 'حجم الملف كبير جداً',
      'networkError': 'خطأ في الاتصال بالشبكة',
      'serverError': 'حدث خطأ في الخادم',
      'unknownError': 'حدث خطأ غير معروف',
      'timeoutError': 'انتهت مهلة الطلب',
      'permissionDenied': 'تم رفض الإذن',
      'locationNotFound': 'لم يتم العثور على الموقع',
      'cameraNotAvailable': 'الكاميرا غير متاحة',
      'microphoneNotAvailable': 'الميكروفون غير متاح',
      'storageNotAvailable': 'التخزين غير متاح',
      'internetRequired': 'الاتصال بالإنترنت مطلوب',
      'gpsRequired': 'موقع GPS مطلوب',
      'bluetoothRequired': 'البلوتوث مطلوب',
      'nfcRequired': 'NFC مطلوب',
      'biometricNotAvailable': 'المصادقة البيومترية غير متاحة',
      'biometricNotEnrolled': 'لا توجد بيانات بيومترية مسجلة',
      'deviceNotSupported': 'الجهاز غير مدعوم',
      'featureNotAvailable': 'الميزة غير متاحة',
      'serviceUnavailable': 'الخدمة غير متاحة',
      'maintenanceMode': 'النظام تحت الصيانة',
      'quotaExceeded': 'تم تجاوز الحد المسموح',
      'rateLimitExceeded': 'تم تجاوز حد المعدل',
      'subscriptionRequired': 'الاشتراك مطلوب',
      'paymentRequired': 'الدفع مطلوب',
      'trialExpired': 'انتهت فترة التجربة',
      'accountNotVerified': 'الحساب غير مُتحقق منه',
      'emailNotVerified': 'البريد الإلكتروني غير مُتحقق منه',
      'phoneNotVerified': 'الهاتف غير مُتحقق منه',
      'documentNotVerified': 'الوثيقة غير مُتحقق منها',
      'identityNotVerified': 'الهوية غير مُتحقق منها',
      'ageVerificationRequired': 'التحقق من العمر مطلوب',
      'parentalConsentRequired': 'موافقة الوالدين مطلوبة',
      'termsNotAccepted': 'شروط الخدمة غير مقبولة',
      'privacyPolicyNotAccepted': 'سياسة الخصوصية غير مقبولة',
      'cookiesNotAccepted': 'ملفات تعريف الارتباط غير مقبولة',
      'locationPermissionRequired': 'إذن الموقع مطلوب',
      'cameraPermissionRequired': 'إذن الكاميرا مطلوب',
      'microphonePermissionRequired': 'إذن الميكروفون مطلوب',
      'storagePermissionRequired': 'إذن التخزين مطلوب',
      'contactsPermissionRequired': 'إذن جهات الاتصال مطلوب',
      'calendarPermissionRequired': 'إذن التقويم مطلوب',
      'notificationPermissionRequired': 'إذن الإشعارات مطلوب',

      // Additional UI Elements
      'textDirection': 'اتجاه النص',
      'rtl': 'من اليمين إلى اليسار',
      'ltr': 'من اليسار إلى اليمين',
      'backgroundWorkEnabled': 'العمل في الخلفية',
    },
  };
}
