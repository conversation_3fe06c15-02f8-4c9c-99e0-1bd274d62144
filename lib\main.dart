import 'package:deewan/app/services/services_impl/app_settings_service.dart';
import 'package:refreshed/refreshed.dart';
import 'package:deewan/app/services/services_impl/localization_service.dart';
import 'package:deewan/core/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Services are now initialized lazily via InitialBindings
  // No need for manual service initialization
  runApp(const Deewan());
}

// ignore: must_be_immutable

class Deewan extends StatelessWidget {
  const Deewan({super.key});
  

  @override
  Widget build(BuildContext context) {
    return Obx(
      () =>  GetMaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'title'.tr,
        
          // Theme configuration
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,

          themeMode: Get.find<AppSettingsService>().themeMode,
          locale: LocalizationService.to.currentLocale,
          fallbackLocale: LocalizationService.to.fallbackLocale,
          supportedLocales: const [
            Locale('en', 'US'), // English
            Locale('ar', 'jo'), // Arabic
          ],
          // Flutter's built-in localizations for widget directionality, etc.
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          // RTL support
         
          // translations: 
          // Navigation configuration
          // initialRoute: Routes.INITIAL,
          // getPages: AppPages.routes,
          defaultTransition: Transition.fade,  
        
        ),
    );
  }
}
